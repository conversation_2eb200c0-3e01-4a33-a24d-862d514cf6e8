# FastScan - Memory Bank Document

## Project Overview

- **Goal:** A security vulnerability scanning platform that leverages Nuclei scanner to identify security vulnerabilities, misconfigurations, and exposures in web applications and infrastructure.
- **Monorepo Tooling:** Standard package management with npm/yarn
- **Primary Technologies (Overall):**
  - Frontend: React, TypeScript, Vite, TailwindCSS, TanStack (React Query, Router, Form, Table)
  - Backend: Placeholder (not yet implemented)
  - Infrastructure: AWS Lambda, S3, DynamoDB, Glue, Terraform
  - Scanner: Nuclei (via Nuclear Pond)

## Monorepo Root Structure

- `/frontend/`: React-based web application for the user interface
- `/backend/`: Placeholder for future backend implementation
- `/nuclear_pond/`: Go-based implementation of Nuclear Pond, a cloud-based Nuclei scanner
- `/terraform/`: Terraform configuration for deploying Nuclear Pond infrastructure

**Key Configuration Files:**

- `Root package.json`: Basic project configuration
- `.devcontainer/`: Development container configuration

## Subprojects

### Subproject: frontend

- **Location:** `/frontend`
- **Type:** Web Application
- **Purpose:** Provides a user interface for managing security scans, viewing scan results, and analyzing vulnerabilities
- **Key Technologies:**
  - React 19
  - TypeScript
  - Vite
  - TailwindCSS/DaisyUI
  - TanStack (React Query, Router, Form, Table)
  - Chart.js
  - Jotai (state management)
- **Internal Dependencies:** None currently implemented
- **Key External Dependencies:** None explicitly defined beyond frontend libraries
- **Notes:** The frontend is structured with a component-based architecture and includes features for authentication, dashboard visualization, scan management, and vulnerability reporting.

### Subproject: backend

- **Location:** `/backend`
- **Type:** Service (Placeholder)
- **Purpose:** Will serve as the API backend for the frontend application
- **Key Technologies:** Not yet implemented
- **Internal Dependencies:** None yet
- **Key External Dependencies:** None yet
- **Notes:** Currently just a placeholder directory with a readme indicating future implementation.

### Subproject: nuclear_pond

- **Location:** `/nuclear_pond`
- **Type:** CLI Tool/Service
- **Purpose:** A Go-based implementation that leverages Nuclei scanner in AWS Lambda for scalable, cost-effective security scanning
- **Key Technologies:**
  - Go
  - AWS Lambda
  - AWS S3
  - Nuclei scanner
- **Internal Dependencies:** None within the monorepo
- **Key External Dependencies:**
  - AWS Lambda
  - AWS S3
  - Nuclei scanner
  - AWS Athena (for querying results)
- **Notes:** Nuclear Pond allows running Nuclei scans in parallel using AWS Lambda, with results stored in S3 and queryable via Athena. It can be used as a CLI tool or run as an HTTP server to accept scan requests via API.

### Subproject: terraform

- **Location:** `/terraform`
- **Type:** Infrastructure as Code
- **Purpose:** Terraform configuration to deploy the Nuclear Pond infrastructure on AWS
- **Key Technologies:**
  - Terraform
  - AWS (Lambda, S3, DynamoDB, Glue)
- **Internal Dependencies:**
  - Depends on the `nuclear_pond` Go code for the Lambda function
- **Key External Dependencies:**
  - AWS services (Lambda, S3, DynamoDB, Glue, CloudWatch)
  - Nuclei scanner (downloaded during deployment)
- **Notes:** Sets up all necessary AWS resources including Lambda functions, S3 buckets for storing scan results, DynamoDB for scan state, and Glue for querying results. Includes a warning about potential remote code execution vulnerabilities due to the design decision to pass arguments directly to the Nuclei scanner.

## Architecture Summary

FastScan is a security vulnerability scanning platform built around the Nuclei scanner. The system consists of:

- A React-based frontend for user interaction, scan management, and result visualization
- A placeholder for a future backend API service
- Nuclear Pond, a Go-based implementation that runs Nuclei scans in AWS Lambda
- Terraform configuration for deploying the Nuclear Pond infrastructure

The platform allows users to configure and run security scans against web applications and infrastructure, with results stored in AWS S3 and queryable via AWS Athena. The frontend provides visualizations of vulnerability data, including severity distributions, scan history, and detailed vulnerability information.

The project appears to be in active development, with the frontend being the most developed component, while the backend is still a placeholder. The Nuclear Pond component and its Terraform configuration are complete and functional, providing the core scanning functionality.