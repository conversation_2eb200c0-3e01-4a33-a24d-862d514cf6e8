# Frontend Deployment Guide

This comprehensive guide explains how to deploy the frontend of the Nuclei scanning application to AWS. It covers prerequisites, configuration, deployment steps for different environments, manual deployment alternatives, and troubleshooting.

For a high-level overview of deployment scenarios and architectural details, refer to:
- [Frontend Setup Summary](./frontend-setup-summary.md)
- [Frontend Architecture](./frontend-architecture.md)

## 🎯 Deployment Modes

You can deploy the frontend in two primary modes:

### 🛠️ **Development Mode (S3 only)**
- **Description**: Direct S3 static website hosting.
- **Pros**: Simple HTTP hosting, faster deployments (no CDN cache invalidation), lower costs, easier debugging due to direct file access.
- **Use Cases**: Ideal for local development, rapid testing, and internal demos.

### 🚀 **Production Mode (CloudFront + S3)**
- **Description**: Uses S3 for storage with AWS CloudFront as a CDN.
- **Pros**: Global content delivery for fast loading worldwide, HTTPS with free SSL/TLS certificates via ACM, intelligent caching for improved performance and reduced S3 load, DDoS protection (AWS Shield Standard), support for custom domains via Route53.
- **Use Cases**: Best for production, staging environments, and any public-facing deployments.

## 📋 Prerequisites

Before you begin, ensure you have the following installed and configured:

1.  **AWS CLI**: Installed and configured with credentials that have permissions to manage S3, CloudFront, Route53 (if using custom domains), ACM, and IAM.
    ```bash
    # Check installation
    aws --version
    # Configure (if not done already)
    aws configure
    ```
2.  **Node.js and Yarn**: Required for building the React frontend application.
    ```bash
    # Check installation
    node --version
    yarn --version
    ```
    If not installed, download from [nodejs.org](https://nodejs.org/) and [yarnpkg.com](https://yarnpkg.com/).
3.  **Terraform**: Used for provisioning the AWS infrastructure.
    ```bash
    # Check installation
    terraform --version
    ```
    If not installed, download from [terraform.io](https://www.terraform.io/downloads.html).
4.  **Backend API**: The Nuclear Pond backend API should be deployed and accessible. You will need its URL for the frontend configuration.
5.  **Git**: For cloning the repository and managing code.

## ⚙️ Step 1: Configure Your Deployment (`terraform/terraform.tfvars`)

Create or edit the `terraform/terraform.tfvars` file to define your deployment settings. Below are common configurations.

**Core Settings (Required for All Deployments):**
```hcl
project_name = "your-unique-project-name" # Used for naming AWS resources

# API Key for backend services if required by your frontend
nuclearpond_api_key = "your-backend-api-key-if-any" 

# Example application-specific setting
frontend_demo_password = "AStrongPassword123!"

# Enable frontend deployment module
enable_frontend_deployment = true
```

**Choose Your Deployment Mode:**

**A. Development Mode (S3 Only):**
```hcl
frontend_environment       = "dev"
enable_frontend_cloudfront = false
```

**B. Production Mode (CloudFront + S3):**
```hcl
frontend_environment           = "prod"
enable_frontend_cloudfront     = true
frontend_cloudfront_price_class = "PriceClass_100" # Options: PriceClass_100, PriceClass_200, PriceClass_All. Controls cost and edge locations.
```

**Custom Domain Configuration (Optional - for Production Mode with CloudFront):**
To use a custom domain like `app.yourdomain.com`:
1.  Your domain must be managed in an AWS Route53 hosted zone.
2.  Find your Route53 Zone ID: `aws route53 list-hosted-zones --query "HostedZones[?Name=='yourdomain.com.'].Id" --output text` (replace `yourdomain.com.` with your domain).

Add these variables to `terraform.tfvars`:
```hcl
# Ensure these are set for custom domain with production mode:
# frontend_environment        = "prod"
# enable_frontend_cloudfront  = true

frontend_domain_name           = "app.yourdomain.com" # Your desired subdomain
create_frontend_route53_record = true                 # Set to true to create the DNS record
frontend_route53_zone_id       = "Z0123456789ABCDEFGHIJ" # Your Route53 Hosted Zone ID
```

**Backend API URL Configuration:**
The frontend needs to know the URL of your backend API. This is typically configured in environment-specific `.env` files (e.g., `frontend/.env.dev`, `frontend/.env.prod`) which are used by Vite during the build process. Terraform can help generate these.

Example: The deployment scripts might use a variable from `tfvars` to populate `VITE_API_URL` in the `.env` file for the frontend build:
```hcl
# In terraform.tfvars (this is an example, actual variable name might differ based on module setup)
# backend_api_url_for_frontend = "https://api.yourdomain.com" # For prod
# backend_api_url_for_frontend = "http://your-alb-dns-name/api" # For dev
```
*Consult your specific Terraform module's documentation if it manages `.env` file generation for the frontend build.* If not, you'll need to ensure your `frontend/src/.env.{environment}` files are correctly set up before building the frontend code.

## 🛠️ Step 2: Deploy the Infrastructure

These commands will provision the AWS resources (S3 bucket, CloudFront distribution, etc.) based on your `terraform.tfvars` configuration.

```bash
# Navigate to the Terraform configuration directory
cd terraform

# Initialize Terraform (run once per project or after adding new modules)
terraform init

# (Optional) Plan the deployment to see what resources will be created/modified
terraform plan -target=module.frontend

# Apply the configuration to deploy the frontend infrastructure
terraform apply -target=module.frontend

# Confirm by typing 'yes' when prompted
```
After successful application, Terraform will output useful information, such as the S3 bucket name or CloudFront distribution ID. You can also retrieve these using `terraform output`.

## 🚀 Step 3: Build and Deploy Your Frontend Code

Once the infrastructure is ready, Terraform typically generates deployment scripts in the `terraform/frontend/` directory. These scripts handle building your React application (using Yarn) and syncing the static files to the S3 bucket.

```bash
# Navigate to the directory containing the auto-generated deployment scripts
cd terraform/frontend

# Make the scripts executable (if needed, usually done once)
# chmod +x deploy-frontend-dev.sh
# chmod +x deploy-frontend-prod.sh

# Run the deployment script corresponding to your configured environment
# For Development Mode (if frontend_environment = "dev")
./deploy-frontend-dev.sh

# OR

# For Production Mode (if frontend_environment = "prod")
./deploy-frontend-prod.sh
```
These scripts usually perform the following actions:
1.  Navigate to the `frontend` source code directory (e.g., `../../frontend`).
2.  Install Node.js dependencies (`yarn install`).
3.  Run the appropriate build command (e.g., `yarn build:dev` or `yarn build:prod`). This creates static assets in the `frontend/dist` directory.
4.  Sync the contents of `frontend/dist` to the S3 bucket created by Terraform using `aws s3 sync ... --delete`.
5.  If CloudFront is enabled, create a cache invalidation to ensure users receive the latest version.

## 🌐 Step 4: Access Your Application

After the deployment script completes, you can get the URL of your application from Terraform outputs.

```bash
# Navigate back to the terraform directory if you are in terraform/frontend
cd ..

# Get the primary frontend URL
terraform output frontend_url

# (Optional) Get all frontend related URLs (e.g., S3 website endpoint, CloudFront domain)
terraform output all_frontend_urls
```
Open the displayed URL in your browser. If you configured a custom domain, allow some time for DNS propagation (usually a few minutes, but can take longer).

## 🔧 Manual Build and Deployment (Alternative)

If you need more control or the auto-generated scripts are not suitable for your workflow, you can build and deploy manually.

### 1. Build the Frontend Application
```bash
# Navigate to your frontend application's source code directory
cd frontend # Or your equivalent frontend source folder

# Install dependencies (if not already done or if they changed)
yarn install

# Build for the target environment (creates files in ./dist/)
# Ensure your .env.{environment} files (e.g., .env.dev, .env.prod) are correctly configured
yarn build:dev     # For development environment
# yarn build:staging # Example for another environment
yarn build:prod    # For production environment
```

### 2. Deploy Static Files to S3
```bash
# Get your S3 bucket name from Terraform outputs
# (Run this from the `terraform` directory)
S3_BUCKET_NAME=$(terraform output -raw frontend_s3_bucket_name)

# Sync the built assets from your frontend's dist/ directory to S3
# (Run this from the directory where your `dist` folder is, e.g., `frontend`)
aws s3 sync dist/ s3://${S3_BUCKET_NAME} --delete

# If you need to set specific cache-control headers or other metadata, 
# you can use additional options with aws s3 sync or aws s3 cp.
# Example for setting cache-control for all uploaded files:
# aws s3 sync dist/ s3://${S3_BUCKET_NAME} --delete --metadata-directive REPLACE --cache-control max-age=3600
```

### 3. Invalidate CloudFront Cache (If Using CloudFront)
This step is crucial if you are using CloudFront to ensure users see the updated content, not a cached version.
```bash
# Get your CloudFront Distribution ID from Terraform outputs
# (Run this from the `terraform` directory)
CLOUDFRONT_ID=$(terraform output -raw cloudfront_distribution_id)

# Check if CLOUDFRONT_ID is not null (i.e., CloudFront is enabled)
if [ -n "$CLOUDFRONT_ID" ] && [ "$CLOUDFRONT_ID" != "null" ]; then
  echo "Creating CloudFront invalidation for distribution: $CLOUDFRONT_ID"
  aws cloudfront create-invalidation --distribution-id $CLOUDFRONT_ID --paths "/*"
else
  echo "CloudFront not enabled or ID not found. Skipping invalidation."
fi
```

## 🔄 Common Workflows

### Updating Frontend Code (No Infrastructure Changes)
If you've only changed frontend code (React components, styles, etc.) and not the infrastructure configuration:
1.  Ensure your `terraform/terraform.tfvars` reflects the correct `frontend_environment`.
2.  Navigate to `terraform/frontend/`.
3.  Run the appropriate deployment script: `./deploy-frontend-dev.sh` or `./deploy-frontend-prod.sh`.
    Alternatively, follow the manual build and deployment steps.

### Switching from Development to Production Mode
1.  **Update `terraform/terraform.tfvars`**:
    ```hcl
    frontend_environment       = "prod"
    enable_frontend_cloudfront = true
    # Add/update frontend_cloudfront_price_class if desired
    # Add/update custom domain variables if applicable (frontend_domain_name, etc.)
    ```
2.  **Deploy Infrastructure Changes**:
    ```bash
    cd terraform
    terraform init # If you haven't or if modules changed
    terraform apply -target=module.frontend
    ```
3.  **Build and Deploy Frontend Code for Production**:
    ```bash
    cd terraform/frontend
    ./deploy-frontend-prod.sh
    ```
4.  **DNS Configuration**: If you added a custom domain, ensure your DNS settings in Route53 are correct. Propagation might take time.

### Adding a Custom Domain to an Existing Production Setup
1.  Ensure your domain is in Route53 and you have the Zone ID.
2.  **Update `terraform/terraform.tfvars`**:
    ```hcl
    # Ensure these are already set for production:
    # frontend_environment       = "prod"
    # enable_frontend_cloudfront = true

    frontend_domain_name           = "app.yournewdomain.com"
    create_frontend_route53_record = true
    frontend_route53_zone_id       = "YOUR_NEW_ROUTE53_ZONE_ID"
    ```
3.  **Deploy Infrastructure Changes**:
    ```bash
    cd terraform
    terraform apply -target=module.frontend
    ```
    This will create the ACM certificate (if it doesn't exist and is validated) and the Route53 record.
4.  The frontend code itself doesn't need a redeploy unless its internal configuration (like API URLs if they change with the domain) needs updating.

## 💰 Cost Architecture

Understanding the cost implications of each deployment mode is important.

### Development Mode Costs (S3 Only)
-   **S3 Storage**: Standard S3 storage rates (e.g., ~$0.023 per GB per month for the first 50TB). Static frontend assets are usually small.
-   **S3 Requests**: Costs for GET, PUT, and other requests (e.g., ~$0.0004 per 1,000 GET requests).
-   **Data Transfer Out from S3**: Data transferred out to the internet. AWS provides a free tier (e.g., 100GB/month across all services). Beyond that, rates apply (e.g., ~$0.09 per GB for the first 10TB).
-   **Total**: Typically very low, often falling within the AWS Free Tier or costing less than $1-5 per month for small projects with limited traffic.

### Production Mode Costs (CloudFront + S3)
-   **S3 Costs**: Same as development mode for storage and requests *from CloudFront to S3 (origin fetches)*. Data transfer from S3 to CloudFront is generally free.
-   **CloudFront Data Transfer Out**: This is usually the largest cost component. Rates vary by region and usage volume (e.g., ~$0.085 per GB for the first 10TB in US/Europe).
-   **CloudFront HTTP/HTTPS Requests**: A charge per 10,000 requests (e.g., ~$0.0075 to ~$0.0100 per 10,000 HTTPS requests in US/Europe).
-   **ACM SSL Certificates**: Free when used with CloudFront.
-   **Route53 (if using custom domain)**:
    -   Hosted Zone: ~$0.50 per month per hosted zone.
    -   DNS Queries: ~$0.40 per million queries.
-   **Total**: Varies significantly based on traffic. For a small to medium application, it could range from $5 to $50+ per month. High-traffic sites will incur higher CloudFront data transfer costs.
    -   `frontend_cloudfront_price_class` (`PriceClass_100`, `PriceClass_200`, `PriceClass_All`) affects which CloudFront edge locations are used and can influence performance and cost. `PriceClass_100` (US, Canada, Europe) is often the most cost-effective for targeted audiences.

**Monitoring Costs**: Use AWS Cost Explorer to track and analyze your frontend-related expenses. Set up budgets and alerts.

## 🚨 Troubleshooting

### Terraform Issues
-   **Error: `terraform init` fails**: Check internet connectivity, Terraform version, and backend configuration (if using a remote backend like S3).
-   **Error: `terraform apply` fails**: Carefully read the error message. Common issues include IAM permission problems, incorrect variable values in `terraform.tfvars`, or resource conflicts.
    -   Use `terraform plan` to see changes before applying.
    -   For IAM issues, ensure your AWS CLI user/role has necessary permissions (S3FullAccess, CloudFrontFullAccess, ACMPublicCertificateFullAccess, Route53FullAccess if applicable, etc.).

### Deployment Script (`deploy-frontend-*.sh`) Issues
-   **Error: Script not found or permission denied**: Ensure you are in the `terraform/frontend/` directory and the script is executable (`chmod +x script-name.sh`).
-   **Error: `yarn install` or `yarn build` fails**: Check Node.js/Yarn versions. Delete `node_modules` and `yarn.lock` in the `frontend` directory and try `yarn install` again. Look for specific error messages from Yarn or Vite.
-   **Error: `aws s3 sync` fails with "AccessDenied"**: 
    -   Verify AWS CLI configuration (`aws sts get-caller-identity` should show the identity Terraform used).
    -   Check IAM permissions for S3 write access to the target bucket.
    -   Ensure the S3 bucket name (from `terraform output frontend_s3_bucket_name`) is correct.
-   **Error: CloudFront invalidation fails**: 
    -   Verify CloudFront distribution ID (from `terraform output cloudfront_distribution_id`).
    -   Check IAM permissions for `cloudfront:CreateInvalidation`.
    -   Invalidations can take several minutes to complete.

### Application Access Issues
-   **Error: 403 Forbidden or 404 Not Found on S3 URL (Dev Mode)**:
    -   Check S3 bucket static website hosting is enabled and the index document is set (usually `index.html`).
    -   Verify bucket policy allows public read access.
    -   Ensure files were uploaded correctly to S3.
-   **Error: 403 Forbidden or other errors on CloudFront URL (Prod Mode)**:
    -   If using Origin Access Control (OAC), ensure it's correctly configured and CloudFront has permission to read from S3.
    -   Check CloudFront distribution status in the AWS console (should be "Deployed").
    -   For custom domains, verify DNS propagation using tools like `dig` or `nslookup`. The CNAME/ALIAS should point to the CloudFront domain.
    -   Check ACM certificate status (should be "Issued" and associated with the CloudFront distribution).
    -   CloudFront cache: Try opening in an incognito window or clearing browser cache. If you just deployed, the invalidation might still be in progress.
-   **Error: Content Security Policy (CSP) issues**: If you have CSP headers, they might block certain resources. Check browser console for errors.
-   **Error: CORS issues when frontend calls backend API**: Ensure the backend API has correct CORS headers configured to allow requests from your frontend's domain (S3 website URL or CloudFront custom domain).

### Viewing Logs and Status
-   **Terraform Outputs**: `terraform output` (in the `terraform` directory) shows key resource identifiers and URLs.
-   **S3 Bucket**: Check contents and properties in the AWS S3 console.
-   **CloudFront Distribution**: Check status, behaviors, origins, and error rates in the AWS CloudFront console.
-   **CloudWatch Logs**: If CloudFront access logging is enabled, logs will be in the designated S3 bucket. Lambda@Edge logs (if used) are in CloudWatch Logs.
-   **Browser Developer Tools**: Inspect network requests, console errors, and local storage for client-side issues.

This guide should provide a solid foundation for deploying your frontend. Remember to adapt commands and configurations to your specific project structure and requirements.

