# Frontend Setup Summary

This summary helps you quickly choose a frontend deployment scenario and outlines the main steps. For detailed instructions, configurations, and troubleshooting, please refer to the [Full Frontend Deployment Guide](./frontend-deployment.md).

## 🎯 Choose Your Setup

Review the development and production options below to decide which best fits your needs. The [Frontend Architecture](./frontend-architecture.md) document provides more detail on the components involved in each setup.

### 🛠️ Development/Testing Setup

**Best for**: Development, testing, internal demos.
**Key Features**:
- ✅ Direct S3 hosting (HTTP)
- ✅ Fast deployments
- ✅ Very low cost
- ✅ Simple debugging

**Primary Configuration in `terraform.tfvars`**:
```hcl
enable_frontend_deployment = true
frontend_environment       = "dev"
enable_frontend_cloudfront = false
```
*For all required variables, see the [configuration section in the deployment guide](./frontend-deployment.md#step-1-configure-your-deployment).*

---

### 🌐 Production Setup

**Best for**: Production, staging, public-facing applications.
**Key Features**:
- ✅ Global CloudFront CDN
- ✅ HTTPS with free SSL
- ✅ High performance worldwide
- ✅ DDoS protection
- ✅ Custom domain support

**Primary Configuration in `terraform.tfvars`**:
```hcl
enable_frontend_deployment     = true
frontend_environment           = "prod"
enable_frontend_cloudfront     = true
# For custom domains, additional variables are needed:
# frontend_domain_name           = "app.yourdomain.com"
# create_frontend_route53_record = true
# frontend_route53_zone_id       = "YOUR_ROUTE53_ZONE_ID"
```
*For all required variables and custom domain setup, see the [configuration section in the deployment guide](./frontend-deployment.md#step-1-configure-your-deployment).*

## 🚀 Quick Process Overview

Deploying the frontend involves these main stages. Detailed commands and explanations are in the [Full Frontend Deployment Guide](./frontend-deployment.md).

### 1. Configure
Edit `terraform/terraform.tfvars` with your chosen settings. Key variables include `project_name`, `nuclearpond_api_key`, `frontend_demo_password`, and the environment-specific variables noted above.

### 2. Deploy Infrastructure
Use Terraform to create the necessary AWS resources (S3 bucket, CloudFront distribution, etc.).
```bash
# Example (see deployment guide for full commands)
cd terraform
terraform init
terraform apply -target=module.frontend
```

### 3. Deploy Frontend Code
Build your frontend application and upload the static files to S3 using the auto-generated deployment scripts.
```bash
# Example (see deployment guide for full commands)
cd terraform/frontend
./deploy-frontend-{environment}.sh 
```
*(Replace `{environment}` with `dev` or `prod`)*

### 4. Access Your App
Retrieve the URL of your deployed application.
```bash
# Example (see deployment guide for full commands)
terraform output frontend_url
```

## 📚 Further Reading

*   **Detailed Deployment Steps**: [frontend-deployment.md](./frontend-deployment.md)
*   **Architectural Details**: [frontend-architecture.md](./frontend-architecture.md)
*   **Auto-generated Help**: `terraform/frontend/DEPLOYMENT-{ENV}.md` (created after infrastructure deployment, provides environment-specific details)

## 🚨 Common Gotchas

1. **Custom domains require CloudFront** - You can't use custom domains with S3-only setup
2. **Route53 zone ID** - Make sure you have the correct zone ID for your domain
3. **Environment naming** - Environment names affect URLs and resource naming
4. **API URL** - Make sure your frontend can reach your backend API from the deployed location
