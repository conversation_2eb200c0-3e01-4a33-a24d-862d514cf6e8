import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import fs from "fs";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // Load environment variables from .env file based on mode
  loadEnv(mode, process.cwd(), "");

  // Check if .env.{mode} file exists, if not create it with default values
  const envFile = `.env.${mode}`;
  if (!fs.existsSync(envFile)) {
    console.log(`Creating ${envFile} with default values...`);
    const defaultEnv = `VITE_API_URL=http://localhost:8080
VITE_API_KEY=testKey
VITE_DEMO_PASSWORD=TestPass
VITE_ENVIRONMENT=${mode}`;
    fs.writeFileSync(envFile, defaultEnv);
  }

  return {
    server: {
      host: "::",
      port: 8000,
    },
    plugins: [
      react()
    ].filter(<PERSON><PERSON>an),
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
    },
    // Define environment variables
    define: {
      __APP_ENV__: JSON.stringify(mode),
    },
    // Build configuration
    build: {
      outDir: "dist",
      sourcemap: mode !== "production",
      // Minify for production
      minify: mode === "production" ? "esbuild" : false,
      // Generate manifest file for asset handling
      manifest: true,
      // Rollup options
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: [
              "react",
              "react-dom",
              "react-router-dom",
              "@tanstack/react-query"
            ],
            ui: [
              "@radix-ui/react-accordion",
              "@radix-ui/react-alert-dialog",
              "@radix-ui/react-avatar",
              "@radix-ui/react-checkbox",
              "@radix-ui/react-dialog",
              "@radix-ui/react-dropdown-menu",
              "@radix-ui/react-label",
              "@radix-ui/react-popover",
              "@radix-ui/react-select",
              "@radix-ui/react-separator",
              "@radix-ui/react-slot",
              "@radix-ui/react-tabs",
              "@radix-ui/react-toast",
              "class-variance-authority",
              "clsx",
              "lucide-react",
              "tailwind-merge"
            ]
          }
        }
      }
    }
  };
});
