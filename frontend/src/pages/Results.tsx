
import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { SearchIcon, FilterIcon } from 'lucide-react';
import { ScanRequest } from '../types';
import AppLayout from '../components/Layout/AppLayout';
import { Skeleton } from '@/components/ui/skeleton';
import { useScans } from '@/hooks/use-api';

const Results: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('all');

  // Get scan data using our custom hook
  const { data: scans, isLoading } = useScans();

  // Create a scanData object to match the expected structure
  const scanData = scans ? { scans, results: [] } : undefined;

  // Filter scans based on search term and active tab
  const filteredScans = scanData?.scans.filter((scan) => {
    const matchesSearch =
      searchTerm === '' ||
      scan.requestId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      scan.config.targets.some(target => target.toLowerCase().includes(searchTerm.toLowerCase()));

    if (activeTab === 'all') return matchesSearch;
    return matchesSearch && scan.status === activeTab;
  }) || [];

  // Render status badge
  const renderScanStatusBadge = (status: string) => {
    switch (status) {
      case 'running':
        return <Badge variant="outline" className="status-running">Running</Badge>;
      case 'completed':
        return <Badge variant="outline" className="status-completed">Completed</Badge>;
      case 'failed':
        return <Badge variant="outline" className="status-failed">Failed</Badge>;
      case 'queued':
        return <Badge variant="outline" className="text-muted-foreground">Queued</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Scan Results</h1>
          <p className="text-muted-foreground mt-2">
            View and manage all your security scan results.
          </p>
        </div>

        <div className="flex flex-col md:flex-row gap-4">
          <div className="w-full md:w-2/3 relative">
            <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search by scan ID or target domain..."
              className="pl-10"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="w-full md:w-1/3">
            <Button variant="outline" className="w-full">
              <FilterIcon className="h-4 w-4 mr-2" />
              Advanced Filter
            </Button>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <div className="border-b">
            <TabsList className="bg-transparent">
              <TabsTrigger value="all" className="data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none">
                All Scans
              </TabsTrigger>
              <TabsTrigger value="running" className="data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none">
                Running
              </TabsTrigger>
              <TabsTrigger value="completed" className="data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none">
                Completed
              </TabsTrigger>
              <TabsTrigger value="failed" className="data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none">
                Failed
              </TabsTrigger>
            </TabsList>
          </div>
        </Tabs>

        <div>
          {isLoading ? (
            <div className="space-y-4">
              {Array.from({ length: 5 }).map((_, i) => (
                <Card key={i}>
                  <CardContent className="p-6">
                    <div className="flex justify-between items-center">
                      <div className="space-y-3 w-full">
                        <Skeleton className="h-5 w-1/3" />
                        <Skeleton className="h-4 w-1/2" />
                        <div className="flex gap-2 mt-2">
                          <Skeleton className="h-6 w-16" />
                          <Skeleton className="h-6 w-16" />
                        </div>
                      </div>
                      <Skeleton className="h-8 w-20" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : filteredScans.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <div className="mx-auto w-12 h-12 rounded-full bg-muted flex items-center justify-center mb-4">
                  <SearchIcon className="h-6 w-6 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-medium">No scans found</h3>
                <p className="text-sm text-muted-foreground mt-2">
                  {searchTerm
                    ? `No scan results match your search term "${searchTerm}"`
                    : `No ${activeTab !== 'all' ? activeTab : ''} scan results found.`}
                </p>
                <Button asChild className="mt-4">
                  <Link to="/scan">Start a New Scan</Link>
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {filteredScans.map((scan) => {
                const scanResult = scanData?.results.find((result) => result.requestId === scan.requestId);

                return (
                  <Card key={scan.requestId} className="hover:bg-muted/30 transition-colors">
                    <CardContent className="p-6">
                      <div className="flex flex-col md:flex-row justify-between md:items-center gap-4">
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <span className="font-mono text-sm font-medium">
                              {scan.requestId}
                            </span>
                            {renderScanStatusBadge(scan.status)}
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {new Date(scan.timestamp).toLocaleString()} |
                            <span className="ml-2">
                              {scan.config.targets.length} target{scan.config.targets.length !== 1 ? 's' : ''}
                            </span>
                          </p>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {scan.config.targets.map((target, i) => (
                              <Badge key={i} variant="outline" className="text-xs">
                                {target}
                              </Badge>
                            ))}
                          </div>

                          {scanResult && (
                            <div className="pt-2 flex flex-wrap gap-3">
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-severity-critical/10 text-severity-critical">
                                {scanResult.summary.criticalCount} Critical
                              </span>
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-severity-high/10 text-severity-high">
                                {scanResult.summary.highCount} High
                              </span>
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-severity-medium/10 text-severity-medium">
                                {scanResult.summary.mediumCount} Medium
                              </span>
                            </div>
                          )}
                        </div>

                        <div className="flex justify-end">
                          <Button asChild variant="secondary">
                            <Link to={`/results/${scan.requestId}`}>
                              View Details
                            </Link>
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </AppLayout>
  );
};

export default Results;
