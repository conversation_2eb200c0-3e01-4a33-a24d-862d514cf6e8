
import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeftIcon, ClockIcon, ChevronDownIcon, ChevronUpIcon } from 'lucide-react';
import { Vulnerability } from '../types';
import AppLayout from '../components/Layout/AppLayout';
import { Separator } from '@/components/ui/separator';
import { useScanStatus, useScanResults } from '@/hooks/use-api';

const ResultDetail: React.FC = () => {
  const { scanId } = useParams<{ scanId: string }>();
  const [expandedVulns, setExpandedVulns] = useState<Record<string, boolean>>({});

  // Fetch scan status and results using our custom hooks
  const { data: scanStatusData, isLoading: scanLoading } = useScanStatus(scanId);

  // Convert the API response to the expected format
  const scan = scanStatusData ? {
    requestId: scanId || '',
    timestamp: new Date().toISOString(),
    status: scanStatusData.status as 'queued' | 'running' | 'completed' | 'failed',
    config: {
      targets: [],
      batches: 1,
      threads: 1,
      output: 's3'
    }
  } : undefined;

  const { data: result, isLoading: resultLoading } = useScanResults(
    scanId,
    !!scanId && scan?.status === 'completed'
  );

  const isLoading = scanLoading || (scan?.status === 'completed' && resultLoading);

  const toggleVulnerability = (vulnId: string) => {
    setExpandedVulns(prev => ({
      ...prev,
      [vulnId]: !prev[vulnId]
    }));
  };

  const renderSeverityBadge = (severity: string) => {
    return <Badge variant="outline" className={`severity-${severity}`}>{severity}</Badge>;
  };

  // Loading state
  if (isLoading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <ClockIcon className="h-10 w-10 mx-auto mb-4 text-muted-foreground animate-pulse" />
            <h2 className="text-2xl font-semibold">Loading scan results...</h2>
          </div>
        </div>
      </AppLayout>
    );
  }

  // Error state
  if (!scan) {
    return (
      <AppLayout>
        <div className="text-center py-12">
          <h2 className="text-2xl font-semibold mb-4">Scan not found</h2>
          <p className="text-muted-foreground mb-8">
            The requested scan could not be found or you don't have permission to view it.
          </p>
          <Button asChild>
            <Link to="/results">Back to Results</Link>
          </Button>
        </div>
      </AppLayout>
    );
  }

  // If scan is still running
  if (scan.status === 'running' || scan.status === 'queued') {
    return (
      <AppLayout>
        <div className="space-y-6">
          <div className="flex items-center">
            <Button variant="ghost" asChild className="mr-4">
              <Link to="/results">
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Back
              </Link>
            </Button>
            <h1 className="text-2xl font-bold">Scan In Progress</h1>
          </div>

          <Card>
            <CardContent className="pt-6 text-center">
              <div className="inline-flex items-center justify-center p-4 bg-muted rounded-full mb-4">
                <ClockIcon className="h-8 w-8 text-muted-foreground animate-pulse" />
              </div>
              <h2 className="text-xl font-semibold mb-2">Scan is still running</h2>
              <p className="text-muted-foreground mb-6">
                The scan is currently in progress. Results will be available when the scan completes.
              </p>
              <div className="flex justify-center gap-4">
                <Button variant="outline">Refresh Status</Button>
                <Button variant="secondary" asChild>
                  <Link to="/results">View All Scans</Link>
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Scan Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">Request ID</p>
                    <p className="font-mono">{scan.requestId}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Status</p>
                    <p className="status-running font-medium">
                      {scan.status.charAt(0).toUpperCase() + scan.status.slice(1)}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Started</p>
                    <p>{new Date(scan.timestamp).toLocaleString()}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Configuration</p>
                    <p>{scan.config.batches} batches, {scan.config.threads} threads</p>
                  </div>
                </div>

                <Separator />

                <div>
                  <h3 className="font-medium mb-2">Targets</h3>
                  <div className="flex flex-wrap gap-2">
                    {scan.config.targets.map((target, i) => (
                      <Badge key={i} variant="outline">{target}</Badge>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </AppLayout>
    );
  }

  // If scan failed
  if (scan.status === 'failed') {
    return (
      <AppLayout>
        <div className="space-y-6">
          <div className="flex items-center">
            <Button variant="ghost" asChild className="mr-4">
              <Link to="/results">
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Back
              </Link>
            </Button>
            <h1 className="text-2xl font-bold">Scan Failed</h1>
          </div>

          <Card className="border-destructive/30">
            <CardContent className="pt-6 text-center">
              <div className="inline-flex items-center justify-center p-4 bg-destructive/10 rounded-full mb-4">
                <ClockIcon className="h-8 w-8 text-destructive" />
              </div>
              <h2 className="text-xl font-semibold mb-2">Scan Failed to Complete</h2>
              <p className="text-muted-foreground mb-6">
                The scan encountered an error and did not complete successfully.
              </p>
              <div className="flex justify-center gap-4">
                <Button variant="outline" asChild>
                  <Link to="/scan">
                    Retry Scan
                  </Link>
                </Button>
                <Button variant="secondary" asChild>
                  <Link to="/results">View All Scans</Link>
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Scan Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">Request ID</p>
                    <p className="font-mono">{scan.requestId}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Status</p>
                    <p className="status-failed font-medium">Failed</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Started</p>
                    <p>{new Date(scan.timestamp).toLocaleString()}</p>
                  </div>
                </div>

                <Separator />

                <div>
                  <h3 className="font-medium mb-2">Targets</h3>
                  <div className="flex flex-wrap gap-2">
                    {scan.config.targets.map((target, i) => (
                      <Badge key={i} variant="outline">{target}</Badge>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </AppLayout>
    );
  }

  // Completed scan with results
  if (result) {
    // Group vulnerabilities by severity for the summary tab
    const vulnerabilitiesBySeverity = {
      critical: result.vulnerabilities.filter(v => v.severity === 'critical'),
      high: result.vulnerabilities.filter(v => v.severity === 'high'),
      medium: result.vulnerabilities.filter(v => v.severity === 'medium'),
      low: result.vulnerabilities.filter(v => v.severity === 'low'),
      info: result.vulnerabilities.filter(v => v.severity === 'info'),
    };

    return (
      <AppLayout>
        <div className="space-y-6">
          <div className="flex items-center">
            <Button variant="ghost" asChild className="mr-4">
              <Link to="/results">
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Back
              </Link>
            </Button>
            <h1 className="text-2xl font-bold">Scan Results</h1>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Scan Summary</CardTitle>
              <CardDescription>
                Overview of vulnerabilities detected during the scan
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                <div className="p-4 bg-severity-critical/10 rounded-md text-center">
                  <p className="text-2xl font-bold text-severity-critical">
                    {result.summary.criticalCount}
                  </p>
                  <p className="text-sm">Critical</p>
                </div>
                <div className="p-4 bg-severity-high/10 rounded-md text-center">
                  <p className="text-2xl font-bold text-severity-high">
                    {result.summary.highCount}
                  </p>
                  <p className="text-sm">High</p>
                </div>
                <div className="p-4 bg-severity-medium/10 rounded-md text-center">
                  <p className="text-2xl font-bold text-severity-medium">
                    {result.summary.mediumCount}
                  </p>
                  <p className="text-sm">Medium</p>
                </div>
                <div className="p-4 bg-severity-low/10 rounded-md text-center">
                  <p className="text-2xl font-bold text-severity-low">
                    {result.summary.lowCount}
                  </p>
                  <p className="text-sm">Low</p>
                </div>
                <div className="p-4 bg-severity-info/10 rounded-md text-center">
                  <p className="text-2xl font-bold text-severity-info">
                    {result.summary.infoCount}
                  </p>
                  <p className="text-sm">Info</p>
                </div>
              </div>

              <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">Request ID</p>
                  <p className="font-mono">{result.requestId}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Completed</p>
                  <p>{new Date(result.timestamp).toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Total Vulnerabilities</p>
                  <p>{result.summary.totalVulnerabilities}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Status</p>
                  <p className="status-completed font-medium">Completed</p>
                </div>
              </div>

              <Separator className="my-6" />

              <div>
                <h3 className="font-medium mb-2">Targets</h3>
                <div className="flex flex-wrap gap-2">
                  {scan.config.targets.map((target, i) => (
                    <Badge key={i} variant="outline">{target}</Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Vulnerability Details</CardTitle>
              <CardDescription>
                Detailed findings from the security scan
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="all">
                <TabsList className="mb-4">
                  <TabsTrigger value="all">All ({result.vulnerabilities.length})</TabsTrigger>
                  <TabsTrigger value="critical">Critical ({vulnerabilitiesBySeverity.critical.length})</TabsTrigger>
                  <TabsTrigger value="high">High ({vulnerabilitiesBySeverity.high.length})</TabsTrigger>
                  <TabsTrigger value="medium">Medium ({vulnerabilitiesBySeverity.medium.length})</TabsTrigger>
                  <TabsTrigger value="low">Low ({vulnerabilitiesBySeverity.low.length})</TabsTrigger>
                  <TabsTrigger value="info">Info ({vulnerabilitiesBySeverity.info.length})</TabsTrigger>
                </TabsList>

                <TabsContent value="all" className="space-y-4">
                  {result.vulnerabilities.map((vuln) => (
                    <VulnerabilityCard
                      key={vuln.id}
                      vulnerability={vuln}
                      isExpanded={!!expandedVulns[vuln.id]}
                      onToggle={() => toggleVulnerability(vuln.id)}
                    />
                  ))}
                </TabsContent>

                <TabsContent value="critical" className="space-y-4">
                  {vulnerabilitiesBySeverity.critical.length > 0
                    ? vulnerabilitiesBySeverity.critical.map((vuln) => (
                        <VulnerabilityCard
                          key={vuln.id}
                          vulnerability={vuln}
                          isExpanded={!!expandedVulns[vuln.id]}
                          onToggle={() => toggleVulnerability(vuln.id)}
                        />
                      ))
                    : <p className="text-center py-6 text-muted-foreground">No critical vulnerabilities found</p>
                  }
                </TabsContent>

                <TabsContent value="high" className="space-y-4">
                  {vulnerabilitiesBySeverity.high.length > 0
                    ? vulnerabilitiesBySeverity.high.map((vuln) => (
                        <VulnerabilityCard
                          key={vuln.id}
                          vulnerability={vuln}
                          isExpanded={!!expandedVulns[vuln.id]}
                          onToggle={() => toggleVulnerability(vuln.id)}
                        />
                      ))
                    : <p className="text-center py-6 text-muted-foreground">No high vulnerabilities found</p>
                  }
                </TabsContent>

                <TabsContent value="medium" className="space-y-4">
                  {vulnerabilitiesBySeverity.medium.length > 0
                    ? vulnerabilitiesBySeverity.medium.map((vuln) => (
                        <VulnerabilityCard
                          key={vuln.id}
                          vulnerability={vuln}
                          isExpanded={!!expandedVulns[vuln.id]}
                          onToggle={() => toggleVulnerability(vuln.id)}
                        />
                      ))
                    : <p className="text-center py-6 text-muted-foreground">No medium vulnerabilities found</p>
                  }
                </TabsContent>

                <TabsContent value="low" className="space-y-4">
                  {vulnerabilitiesBySeverity.low.length > 0
                    ? vulnerabilitiesBySeverity.low.map((vuln) => (
                        <VulnerabilityCard
                          key={vuln.id}
                          vulnerability={vuln}
                          isExpanded={!!expandedVulns[vuln.id]}
                          onToggle={() => toggleVulnerability(vuln.id)}
                        />
                      ))
                    : <p className="text-center py-6 text-muted-foreground">No low vulnerabilities found</p>
                  }
                </TabsContent>

                <TabsContent value="info" className="space-y-4">
                  {vulnerabilitiesBySeverity.info.length > 0
                    ? vulnerabilitiesBySeverity.info.map((vuln) => (
                        <VulnerabilityCard
                          key={vuln.id}
                          vulnerability={vuln}
                          isExpanded={!!expandedVulns[vuln.id]}
                          onToggle={() => toggleVulnerability(vuln.id)}
                        />
                      ))
                    : <p className="text-center py-6 text-muted-foreground">No info vulnerabilities found</p>
                  }
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </AppLayout>
    );
  }

  // Fallback for unexpected state
  return (
    <AppLayout>
      <div className="text-center py-12">
        <h2 className="text-2xl font-semibold mb-4">Unexpected Error</h2>
        <p className="text-muted-foreground mb-8">
          We encountered an unexpected error while loading the scan results.
        </p>
        <Button asChild>
          <Link to="/results">Back to Results</Link>
        </Button>
      </div>
    </AppLayout>
  );
};

// Vulnerability card component
interface VulnerabilityCardProps {
  vulnerability: Vulnerability;
  isExpanded: boolean;
  onToggle: () => void;
}

const VulnerabilityCard: React.FC<VulnerabilityCardProps> = ({
  vulnerability,
  isExpanded,
  onToggle
}) => {
  const severityColorMap = {
    'critical': 'border-severity-critical/30 bg-severity-critical/5',
    'high': 'border-severity-high/30 bg-severity-high/5',
    'medium': 'border-severity-medium/30 bg-severity-medium/5',
    'low': 'border-severity-low/30 bg-severity-low/5',
    'info': 'border-severity-info/30 bg-severity-info/5',
  };

  return (
    <div
      className={`border rounded-md overflow-hidden ${severityColorMap[vulnerability.severity as keyof typeof severityColorMap]}`}
    >
      <div
        className="flex justify-between items-center p-4 cursor-pointer"
        onClick={onToggle}
      >
        <div className="flex items-center">
          <Badge variant="outline" className={`severity-${vulnerability.severity}`}>
            {vulnerability.severity}
          </Badge>
          <h3 className="ml-3 font-medium">{vulnerability.title}</h3>
        </div>
        <div className="flex items-center">
          <Badge variant="outline" className="mr-3">
            {vulnerability.target}
          </Badge>
          <Button variant="ghost" size="icon" onClick={(e) => { e.stopPropagation(); onToggle(); }}>
            {isExpanded ? <ChevronUpIcon className="h-4 w-4" /> : <ChevronDownIcon className="h-4 w-4" />}
          </Button>
        </div>
      </div>

      {isExpanded && (
        <div className="p-4 border-t bg-background">
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium text-muted-foreground mb-1">Description</h4>
              <p>{vulnerability.description}</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="text-sm font-medium text-muted-foreground mb-1">Target</h4>
                <p>{vulnerability.target}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-muted-foreground mb-1">Discovered</h4>
                <p>{new Date(vulnerability.discoveredAt).toLocaleString()}</p>
              </div>
            </div>

            {vulnerability.remediation && (
              <div>
                <h4 className="text-sm font-medium text-muted-foreground mb-1">Remediation</h4>
                <p>{vulnerability.remediation}</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ResultDetail;
