
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { BarChart, Bar, XAxis, YAxis, Tooltip, ResponsiveContainer, Cell, PieChart, Pie } from 'recharts';
import { AlertTriangleIcon, AlertCircleIcon, SearchIcon, ClockIcon } from 'lucide-react';
import { generateMockDashboardMetrics } from '../utils/mockData';
import AppLayout from '../components/Layout/AppLayout';

const Dashboard: React.FC = () => {
  // Mock API call for dashboard data
  const { data: metrics, isLoading } = useQuery({
    queryKey: ['dashboardMetrics'],
    queryFn: async () => {
      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 800));
      return generateMockDashboardMetrics();
    },
  });

  if (isLoading || !metrics) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <ClockIcon className="h-10 w-10 mx-auto mb-4 text-muted-foreground animate-pulse" />
            <h2 className="text-2xl font-semibold">Loading dashboard...</h2>
          </div>
        </div>
      </AppLayout>
    );
  }

  const renderScanStatusBadge = (status: string) => {
    switch (status) {
      case 'running':
        return <Badge variant="outline" className="status-running">Running</Badge>;
      case 'completed':
        return <Badge variant="outline" className="status-completed">Completed</Badge>;
      case 'failed':
        return <Badge variant="outline" className="status-failed">Failed</Badge>;
      case 'queued':
        return <Badge variant="outline" className="text-muted-foreground">Queued</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <h1 className="text-3xl font-bold">Security Dashboard</h1>
          <div className="mt-4 md:mt-0">
            <Button asChild>
              <Link to="/scan">
                <SearchIcon className="h-4 w-4 mr-2" />
                New Scan
              </Link>
            </Button>
          </div>
        </div>

        {/* Metrics cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <div className="p-2 mr-4">
                  <SearchIcon className="h-8 w-8 text-primary" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Total Scans</p>
                  <p className="text-3xl font-bold">{metrics.totalScans}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <div className="p-2 mr-4">
                  <ClockIcon className="h-8 w-8 text-accent" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Active Scans</p>
                  <p className="text-3xl font-bold">{metrics.activeScanCount}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

      
      </div>
    </AppLayout>
  );
};

export default Dashboard;
