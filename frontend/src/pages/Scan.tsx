import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { SearchIcon, TrashIcon, PlusIcon } from 'lucide-react';
import { useInitiateScan } from '@/hooks/use-api';
import AppLayout from '../components/Layout/AppLayout';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';

// Form validation schema
const scanFormSchema = z.object({
  targets: z.array(z.string().url("Please enter a valid URL"))
    .min(1, "At least one target is required"),
  batches: z.number()
    .int("Batches must be a whole number")
    .min(1, "Minimum 1 batch")
    .max(20, "Maximum 20 batches"),
  threads: z.number()
    .int("Threads must be a whole number")
    .min(1, "Minimum 1 thread")
    .max(50, "Maximum 50 threads"),
  output: z.enum(['s3'])
    .default("s3")
});

// Helper to generate a random alphanumeric string
const generateRandomAlphanumericString = (length: number): string => {
  let result = '';
  const characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
  const charactersLength = characters.length;
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
};

type ScanFormValues = z.infer<typeof scanFormSchema>;

const Scan: React.FC = () => {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [targetInput, setTargetInput] = useState("");
  const [bulkTargets, setBulkTargets] = useState("");
  const [numDemoTargets, setNumDemoTargets] = useState<number>(10); // For demo target generation

  const form = useForm<ScanFormValues>({
    resolver: zodResolver(scanFormSchema),
    defaultValues: {
      targets: [],
      batches: 1,
      threads: 1,
      output: "s3",
    },
  });

  const { watch, setValue, getValues } = form;
  const targets = watch("targets");

  // Normalizes potential targets, validates them, and filters out duplicates or already existing ones.
  const normalizeAndValidateTargets = (
    potentialTargets: string[],
    existingTargets: string[]
  ): { addedTargets: string[]; invalidOriginals: string[]; skippedDuplicates: string[] } => {
    const addedTargets: string[] = []; // Stores normalized, valid, unique URLs
    const invalidOriginals: string[] = []; // Stores original inputs that were invalid
    const skippedDuplicates: string[] = []; // Stores original inputs that were duplicates

    // Deduplicate potential targets based on their original form before normalization
    const uniquePotentialOriginals = Array.from(new Set(potentialTargets.map(t => t.trim()).filter(Boolean)));

    uniquePotentialOriginals.forEach(originalTarget => {
      let normalizedTarget = originalTarget;
      // Prepend http:// if no scheme is present, as z.string().url() typically expects a scheme.
      if (!/^[a-zA-Z][a-zA-Z0-9+.-]*:/.test(normalizedTarget)) {
        normalizedTarget = `http://${normalizedTarget}`;
      }

      try {
        new URL(normalizedTarget); // Validate the normalized URL (throws if invalid)

        // Check against existing (which should also be normalized) and already added normalized targets
        if (!existingTargets.includes(normalizedTarget) && !addedTargets.includes(normalizedTarget)) {
          addedTargets.push(normalizedTarget); // Add the normalized, valid, unique URL
        } else {
          skippedDuplicates.push(originalTarget); // Original target was a duplicate
        }
      } catch (e) {
        invalidOriginals.push(originalTarget); // Original target was invalid
      }
    });
    return { addedTargets, invalidOriginals, skippedDuplicates };
  };

  const addTarget = () => {
    const trimmedInput = targetInput.trim();
    if (!trimmedInput) return;

    const currentTargets = getValues("targets"); // Get current targets from form state
    const { addedTargets: [newTarget], invalidOriginals, skippedDuplicates } =
      normalizeAndValidateTargets([trimmedInput], currentTargets);

    if (newTarget) {
      setValue("targets", [...currentTargets, newTarget]);
      setTargetInput("");
      toast({
        title: "Target Added",
        description: `Added: ${newTarget}`,
      });
    } else if (invalidOriginals.length > 0) {
      toast({
        title: "Invalid URL",
        description: `"${invalidOriginals[0]}" is not a valid URL format. Ensure it's a full URL (e.g., http://example.com) or a domain.`,
        variant: "destructive",
      });
    } else if (skippedDuplicates.length > 0) {
      toast({
        title: "Target Already Added",
        description: `"${skippedDuplicates[0]}" is already in the target list.`,
        variant: "info",
      });
      setTargetInput(""); // Clear input even if duplicate
    }
  };

  const removeTarget = (index: number) => {
    setValue(
      "targets",
      targets.filter((_, i) => i !== index)
    );
  };

  const processAndAddBulkTargets = () => {
    if (!bulkTargets.trim()) return;

    const potentialTargetsArray = bulkTargets
      .split(/[\n,; ]+/)
      .map((t) => t.trim())
      .filter(Boolean); // Remove empty strings

    if (potentialTargetsArray.length === 0) {
      setBulkTargets("");
      return;
    }

    const currentTargets = getValues("targets");
    const { addedTargets: newNormalizedTargets, invalidOriginals, skippedDuplicates } =
      normalizeAndValidateTargets(potentialTargetsArray, currentTargets);

    if (invalidOriginals.length > 0) {
      toast({
        title: "Some Targets Invalid",
        description: `Skipped ${invalidOriginals.length} invalid entries (e.g., "${invalidOriginals[0]}"). Valid URLs or domains are required.`,
        variant: "warning",
      });
    }

    if (skippedDuplicates.length > 0 && newNormalizedTargets.length === 0 && invalidOriginals.length === 0) {
      toast({ title: "No New Targets Added", description: `All ${skippedDuplicates.length} inputs were duplicates.`, variant: "info" });
    }


    if (newNormalizedTargets.length > 0) {
      setValue("targets", [...currentTargets, ...newNormalizedTargets]);
      toast({
        title: "Targets Added",
        description: `${newNormalizedTargets.length} new target(s) added from bulk input.`,
      });
    }
    setBulkTargets(""); // Clear textarea in all cases after processing
  };

  const handleGenerateDemoTargets = () => {
    if (numDemoTargets <= 0 || numDemoTargets > 10000) { // Added upper limit for sanity
      toast({ title: "Invalid Number", description: "Please enter a number between 1 and 10,000.", variant: "warning" });
      return;
    }
    const generatedRawTargets: string[] = [];
    for (let i = 0; i < numDemoTargets; i++) {
      const randomSub = generateRandomAlphanumericString(8);
      // No http:// here, normalizeAndValidateTargets will add it.
      generatedRawTargets.push(`${randomSub}.fast-scan-demo-target.click`);
    }

    const currentTargets = getValues("targets");
    const { addedTargets: newNormalizedTargets, invalidOriginals, skippedDuplicates } =
      normalizeAndValidateTargets(generatedRawTargets, currentTargets);

    // It's unlikely for random targets to be invalid or duplicates if generation is correct.
    if (invalidOriginals.length > 0) {
      toast({ title: "Demo Generation Issue", description: `Could not normalize ${invalidOriginals.length} generated targets (unexpected).`, variant: "error" });
    }
    if (skippedDuplicates.length > 0 && newNormalizedTargets.length === 0) {
      toast({ title: "No New Demo Targets", description: `All ${skippedDuplicates.length} generated targets were already in the list (highly unlikely).`, variant: "info" });
    }

    if (newNormalizedTargets.length > 0) {
      setValue("targets", [...currentTargets, ...newNormalizedTargets]);
      toast({
        title: "Demo Targets Added",
        description: `${newNormalizedTargets.length} demo targets added.`,
      });
    } else if (newNormalizedTargets.length === 0 && invalidOriginals.length === 0 && skippedDuplicates.length === 0 && generatedRawTargets.length > 0) {
      toast({ title: "No Demo Targets Added", description: "Could not generate any new demo targets.", variant: "info" });
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) {
      event.target.value = ""; // Reset if no file is chosen (e.g., user cancels dialog)
      return;
    }

    if (file.type !== "text/plain") {
      toast({ title: "Invalid File Type", description: "Please upload a .txt file.", variant: "destructive" });
      event.target.value = ""; // Reset file input
      return;
    }

    const MAX_FILE_SIZE_MB = 10;
    if (file.size > MAX_FILE_SIZE_MB * 1024 * 1024) {
      toast({
        title: "File Too Large",
        description: `File size cannot exceed ${MAX_FILE_SIZE_MB}MB. Consider splitting the file.`,
        variant: "destructive",
      });
      event.target.value = "";
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      if (content) {
        const potentialTargetsArray = content
          .split(/[\n,; ]+/)
          .map((t) => t.trim())
          .filter(Boolean);

        if (potentialTargetsArray.length === 0) {
          toast({ title: "Empty File", description: `The file ${file.name} appears to be empty or contains no valid targets.`, variant: "info" });
          event.target.value = "";
          return;
        }

        const currentTargets = getValues("targets");
        const { addedTargets: newNormalizedTargets, invalidOriginals, skippedDuplicates } =
          normalizeAndValidateTargets(potentialTargetsArray, currentTargets);

        if (invalidOriginals.length > 0) {
          toast({
            title: "Some Targets Invalid in File",
            description: `Skipped ${invalidOriginals.length} invalid entries from ${file.name} (e.g., "${invalidOriginals[0]}").`,
            variant: "warning",
          });
        }
        if (skippedDuplicates.length > 0 && newNormalizedTargets.length === 0 && invalidOriginals.length === 0) {
          toast({ title: "No New Targets From File", description: `All ${skippedDuplicates.length} targets from ${file.name} were duplicates.`, variant: "info" });
        }

        if (newNormalizedTargets.length > 0) {
          setValue("targets", [...currentTargets, ...newNormalizedTargets]);
          toast({
            title: "Targets Added from File",
            description: `${newNormalizedTargets.length} new target(s) added from ${file.name}.`,
          });
        } else if (newNormalizedTargets.length === 0 && invalidOriginals.length === 0 && skippedDuplicates.length === 0 && potentialTargetsArray.length > 0) {
          toast({ title: "No Targets Added", description: `No new targets could be added from ${file.name}. They might all be invalid or duplicates.`, variant: "info" });
        }
      } else {
        toast({ title: "Empty or Unreadable File", description: `Could not read content from ${file.name}.`, variant: "warning" });
      }
      event.target.value = ""; // Reset file input after processing
    };
    reader.onerror = () => {
      toast({ title: "File Read Error", description: `Could not read the selected file: ${file.name}.`, variant: "destructive" });
      event.target.value = "";
    };
    reader.readAsText(file);
  };

  const initiateScanMutation = useInitiateScan();

  const onSubmit = async (data: ScanFormValues) => {
    setIsSubmitting(true);

    try {
      // Call the API using our mutation hook
      const response = await initiateScanMutation.mutateAsync({
        targets: data.targets,
        batches: data.batches,
        threads: data.threads,
        output: data.output,
      });

      console.log("Scan response:", response);

      toast({
        title: "Scan initiated",
        description: `Request ID: ${response.RequestId}`,
      });

      // Reset form
      form.reset({
        targets: [],
        batches: 1,
        threads: 1,
        output: "s3",
      });
    } catch (error) {
      console.error("Scan error:", error);
      toast({
        title: "Error initiating scan",
        description: (error as Error).message || "An unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">New Vulnerability Scan</h1>
          <p className="text-muted-foreground mt-2">
            Configure and run a new security scan against your targets.
          </p>
        </div>

        <Card>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit, (error) => {
              console.error("Form submission error:", error);
            })}>
              <CardHeader>
                <CardTitle>Scan Configuration</CardTitle>
                <CardDescription>
                  Enter the targets you want to scan for vulnerabilities.
                </CardDescription>
              </CardHeader>

              <CardContent className="space-y-6">
                {/* Targets Section */}
                <div className="space-y-4">
                  <Tabs defaultValue="single">
                    <TabsList className="grid w-full grid-cols-4">
                      <TabsTrigger value="single">Single</TabsTrigger>
                      <TabsTrigger value="generate">Generate Demo</TabsTrigger>
                      <TabsTrigger value="bulk" disabled>Bulk Text</TabsTrigger>
                      <TabsTrigger value="upload" disabled>Upload File</TabsTrigger>
                    </TabsList>

                    <TabsContent value="single" className="space-y-4 pt-4">
                      <div className="flex gap-2">
                        <Input
                          placeholder="Enter a domain (e.g., example.com)"
                          value={targetInput}
                          onChange={(e) => setTargetInput(e.target.value)}
                          onKeyDown={(e) => e.key === "Enter" && (e.preventDefault(), addTarget())}
                        />
                        <Button type="button" onClick={addTarget} className="shrink-0">
                          <PlusIcon className="h-4 w-4 mr-2" />
                          Add
                        </Button>
                      </div>

                      <FormMessage>
                        {form.formState.errors.targets?.[0]?.message}
                      </FormMessage>
                    </TabsContent>

                    <TabsContent value="bulk" className="space-y-4 pt-4">
                      <FormItem>
                        <FormLabel>Bulk Add Targets via Text</FormLabel>
                        <FormDescription>
                          Enter multiple domains separated by commas or new lines
                        </FormDescription>
                        <Textarea
                          placeholder="example.com, example.org, example.net"
                          value={bulkTargets}
                          onChange={(e) => setBulkTargets(e.target.value)}
                          rows={4}
                        />
                        <Button
                          type="button"
                          variant="secondary"
                          onClick={processAndAddBulkTargets}
                          className="mt-2"
                        >
                          <PlusIcon className="h-4 w-4 mr-2" />
                          Add All Targets
                        </Button>
                      </FormItem>
                    </TabsContent>

                    <TabsContent value="generate" className="space-y-4 pt-4">
                      <FormItem>
                        <FormLabel>Generate Demo Targets</FormLabel>
                        <FormDescription>
                          Generate random targets on the <code>fast-scan-demo-target.click</code> domain.
                        </FormDescription>
                        <div className="flex gap-2 items-end mt-2">
                          <div className="flex-grow">
                            <FormLabel htmlFor="numDemoTargetsInput" className="text-sm font-medium sr-only">Number of targets</FormLabel>
                            <Input
                              id="numDemoTargetsInput"
                              type="number"
                              min={1}
                              max={10000}
                              value={numDemoTargets}
                              onChange={(e) => setNumDemoTargets(parseInt(e.target.value, 10) || 1)}
                              placeholder="Number of targets (1-10000)"
                            />
                          </div>
                          <Button
                            type="button"
                            variant="secondary"
                            onClick={handleGenerateDemoTargets}
                            className="shrink-0"
                          >
                            <PlusIcon className="h-4 w-4 mr-2" />
                            Generate and Add
                          </Button>
                        </div>
                      </FormItem>
                    </TabsContent>

                    <TabsContent value="upload" className="space-y-4 pt-4">
                      <FormItem>
                        <FormLabel>Upload Targets from File</FormLabel>
                        <FormDescription>
                          Upload a <code>.txt</code> file with targets separated by new lines, commas, or spaces. Max file size: 10MB.
                        </FormDescription>
                        <Input
                          type="file"
                          accept=".txt"
                          onChange={handleFileUpload}
                          className="mt-2 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-primary-foreground file:text-primary hover:file:bg-primary-foreground/80"
                        />
                      </FormItem>
                    </TabsContent>
                  </Tabs>

                  <div>
                    <FormLabel>Target List ({targets.length} total)</FormLabel>
                    {targets.length > 0 ? (
                      <div className="border rounded-md p-3 mt-2 space-y-3">
                        <div className="flex flex-wrap gap-2">
                          {targets.slice(0, 50).map((target, index) => (
                            <Badge key={`${target}-${index}`} className="pr-1 flex items-center group">
                              <span className="truncate max-w-[200px] group-hover:max-w-none transition-all duration-200 ease-in-out" title={target}>{target}</span>
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                className="h-5 w-5 ml-1 opacity-50 group-hover:opacity-100"
                                onClick={() => removeTarget(index)}
                              >
                                <TrashIcon className="h-3 w-3" />
                              </Button>
                            </Badge>
                          ))}
                          {targets.length > 50 && (
                            <Badge variant="outline">+{targets.length - 50} more targets</Badge>
                          )}
                        </div>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          className="mt-3"
                          onClick={() => {
                            setValue("targets", []);
                            toast({ title: "Target List Cleared", description: "All targets have been removed." });
                          }}
                        >
                          Clear All {targets.length} Target(s)
                        </Button>
                      </div>
                    ) : (
                      <div className="text-muted-foreground text-sm mt-2">
                        No targets added yet. Add at least one target to scan.
                      </div>
                    )}
                  </div>
                </div>

                <Separator />

                {/* Scan Parameters */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="batches"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Batches</FormLabel>
                        <FormDescription>
                          Number of targets per batch.
                        </FormDescription>
                        <FormControl>
                          <Input
                            type="number"
                            min={1}
                            max={20}
                            {...field}
                            onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="threads"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Threads</FormLabel>
                        <FormDescription>
                          Number of parallel threads to run.
                        </FormDescription>
                        <FormControl>
                          <Input
                            type="number"
                            min={1}
                            max={50}
                            {...field}
                            onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                            disabled={targets.length === 0}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>

              <CardFooter className="flex flex-col gap-4">
                <div className="text-sm text-muted-foreground">
                  <strong>Note:</strong> Scanning targets without proper authorization may violate laws and regulations.
                  Always ensure you have permission to scan the targets.
                </div>

                <Button
                  type="submit"
                  disabled={isSubmitting || targets.length === 0}
                  className="w-full"
                >
                  {isSubmitting ? (
                    <>Processing...</>
                  ) : (
                    <>
                      <SearchIcon className="h-4 w-4 mr-2" />
                      Start Scan
                    </>
                  )}
                </Button>
              </CardFooter>
            </form>
          </Form>
        </Card>
      </div>
    </AppLayout>
  );
};

export default Scan;
