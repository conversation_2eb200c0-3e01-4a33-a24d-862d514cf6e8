import { ScanConfig, <PERSON>anR<PERSON>ult } from "../types";

// Get the API URL from environment variables
const apiUrl = import.meta.env.VITE_API_URL;

const apiKey = import.meta.env.VITE_API_KEY;

// Check if API URL is defined
if (!apiUrl) {
  console.warn("API URL is not defined in environment variables. Set VITE_API_URL in your .env file.");
}

const getApiKey = (): string | null => {
  const authData = localStorage.getItem('auth');
  console.log("authData", authData);
  if (!authData || !apiKey) return null;

  return apiKey;
};

// Helper function to handle API responses
const handleResponse = async <T>(response: Response): Promise<T> => {
  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(errorText || `API error: ${response.status}`);
  }
  
  return response.json() as Promise<T>;
};

// API client for scans
export const scanApi = {
  // Initiate a new scan
  initiateScan: async (config: ScanConfig): Promise<{ RequestId: string }> => {
    const apiKey = getApiKey();
    
    // Format the request body according to the API spec
    const requestBody = {
      Targets: config.targets,
      Batches: config.batches,
      Threads: config.threads,
      Output: config.output,
      Args: "", // Optional, can be added if needed
    };
    
    const response = await fetch(`${apiUrl}/scan`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-NuclearPond-API-Key': apiKey || '',
      },
      body: JSON.stringify(requestBody),
    });
    
    return handleResponse<{ RequestId: string }>(response);
  },
  
  // Get scan status
  getScanStatus: async (scanId: string): Promise<{ status: string }> => {
    const apiKey = getApiKey();
    
    const response = await fetch(`${apiUrl}/scan/${scanId}`, {
      method: 'GET',
      headers: {
        'X-NuclearPond-API-Key': apiKey || '',
      },
    });
    
    return handleResponse<{ status: string }>(response);
  },
  
  // Get scan results (this would need to be implemented on the backend)
  // For now, we'll keep the mock implementation
  getScanResults: async (scanId: string): Promise<ScanResult> => {
    // This is a placeholder - in a real implementation, you would fetch from the API
    // The backend would need to provide an endpoint to get scan results
    throw new Error("API endpoint for scan results not implemented");
  },
};

