import React from 'react';
import { useAtom } from 'jotai';
import { authAtom, logout } from '../../state/auth';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { BellIcon, ShieldIcon, UserIcon } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const Header: React.FC = () => {
  const [_auth, setAuth] = useAtom(authAtom);
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const handleLogout = () => {
    logout();
    setAuth({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null
    });
    
    toast({
      title: 'Logged out',
      description: 'You have been successfully logged out.',
    });
    
    navigate('/login');
  };
  
  return (
    <header className="bg-card border-b border-border p-4 flex items-center justify-between">
      <div className="flex items-center">
        <div className="md:hidden">
          {/* Mobile menu button would go here */}
        </div>
      </div>
      
      <div className="flex items-center space-x-4">
        <Button variant="ghost" size="icon" className="relative">
          <BellIcon className="h-5 w-5" />
        </Button>
        
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm" onClick={handleLogout}>
            <UserIcon className="h-4 w-4 mr-2" />
            Logout
          </Button>
        </div>
      </div>
    </header>
  );
};

export default Header;
