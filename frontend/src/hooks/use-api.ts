import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { scanApi } from "@/lib/api-client";
import { ScanConfig, ScanRequest, ScanResult } from "../types";

// Choose the appropriate API implementation
const api = scanApi;

// Hook for initiating a scan
export const useInitiateScan = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (config: ScanConfig) => api.initiateScan(config),
    onSuccess: () => {
      // Invalidate relevant queries when a new scan is initiated
      queryClient.invalidateQueries({ queryKey: ['scans'] });
    },
  });
};

// Hook for getting scan status
export const useScanStatus = (scanId: string | undefined) => {
  return useQuery({
    queryKey: ['scanStatus', scanId],
    queryFn: () => scanId ? api.getScanStatus(scanId) : Promise.reject("No scan ID provided"),
    enabled: !!scanId,
    // refetchInterval: ({status}) => {
    //   // Refetch every 5 seconds if the scan is still running or queued
    //   if (status === 'running' || status === 'queued') {
    //     return 5000;
    //   }
    //   // Don't refetch if the scan is completed or failed
    //   return false;
    // },
  });
};

// Hook for getting scan results
export const useScanResults = (scanId: string | undefined, enabled = true) => {
  return useQuery({
    queryKey: ['scanResult', scanId],
    queryFn: () => scanId ? api.getScanResults(scanId) : Promise.reject("No scan ID provided"),
    enabled: !!scanId && enabled,
  });
};

// Hook for getting all scans (this would need a backend endpoint)
// For now, we'll use a mock implementation
export const useScans = () => {
  return useQuery({
    queryKey: ['scans'],
    queryFn: async () => {
      // This would normally fetch from an API
      // For demo purposes, we'll create mock data
      await new Promise((resolve) => setTimeout(resolve, 800));
      
      // Generate some mock scan requests
      const mockScans: ScanRequest[] = Array.from({ length: 10 }, (_, i) => {
        const statuses: Array<ScanRequest['status']> = ['queued', 'running', 'completed', 'failed'];
        const status = statuses[Math.floor(Math.random() * statuses.length)];
        
        return {
          requestId: `scan-${Date.now()}-${i}`,
          timestamp: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
          status,
          config: {
            targets: ['example.com', 'test-site.org'],
            batches: Math.floor(Math.random() * 5) + 1,
            threads: Math.floor(Math.random() * 10) + 1,
            output: 's3',
          },
        };
      });
      
      return mockScans;
    },
  });
};
