import { User, AuthState } from '../types';
import { atomWithStorage } from 'jotai/utils';

// Demo hardcoded user
const demoUser = {
  id: 'demo-user-123',
};

// Initial auth state
const initialAuthState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
};

// Load initial state from localStorage if available
const getInitialState = (): AuthState => {
  if (typeof window === 'undefined') return initialAuthState;
  
  const savedAuth = localStorage.getItem('auth');
  if (savedAuth) {
    try {
      return JSON.parse(savedAuth);
    } catch (e) {
      localStorage.removeItem('auth');
    }
  }
  return initialAuthState;
};

// Auth state atom with persistence
export const authAtom = atomWithStorage<AuthState>('auth', getInitialState());

// Login function that validates against hardcoded credentials
export const login = async (email: string, password: string) => {
  // Demo credentials
  const emails = ['<EMAIL>', '<EMAIL>'];
  const _password = import.meta.env.VITE_DEMO_PASSWORD;
  if (emails.includes(email) && password === _password) {
    return {
      ...demoUser,
      email: email,
      name: email.split('@')[0].charAt(0).toUpperCase() + email.split('@')[0].slice(1),
    }
  }
  throw new Error('Invalid email or password');
};

// Logout function
export const logout = () => {
  localStorage.removeItem('auth');
  return true;
};
