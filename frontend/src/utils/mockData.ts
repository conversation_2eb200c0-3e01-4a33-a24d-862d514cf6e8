
import type { Vulnerability, ScanRequest, ScanResult } from '../types';

// Generate a random number between min and max (inclusive)
const randomInt = (min: number, max: number) => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

// Generate mock vulnerabilities
export const generateMockVulnerabilities = (count = randomInt(5, 20)): Vulnerability[] => {
  const severities: Array<Vulnerability['severity']> = ['critical', 'high', 'medium', 'low', 'info'];
  const vulnerabilityTitles = [
    'SQL Injection Vulnerability',
    'Cross-Site Scripting (XSS)',
    'Sensitive Data Exposure',
    'XML External Entity (XXE) Injection',
    'Broken Access Control',
    'Security Misconfiguration',
    'Cross-Site Request Forgery (CSRF)',
    'Using Components with Known Vulnerabilities',
    'Unvalidated Redirects and Forwards',
    'Insecure Deserialization',
    'Insufficient Logging & Monitoring',
    'Server-Side Request Forgery',
    'API Keys Exposed in Client-Side Code',
    'Missing HTTP Security Headers',
    'Open Redirect Vulnerability'
  ];

  const domains = [
    'example.com',
    'test-site.org',
    'demo-application.net',
    'staging-env.io',
    'internal-api.corp'
  ];

  return Array.from({ length: count }, (_, i) => {
    const severity = severities[Math.floor(Math.random() * severities.length)];
    const title = vulnerabilityTitles[Math.floor(Math.random() * vulnerabilityTitles.length)];
    const domain = domains[Math.floor(Math.random() * domains.length)];
    
    return {
      id: `vuln-${Date.now()}-${i}`,
      target: domain,
      title,
      description: `This is a detailed description of the ${title.toLowerCase()} that was discovered on ${domain}.`,
      severity,
      discoveredAt: new Date(Date.now() - randomInt(0, 30 * 24 * 60 * 60 * 1000)).toISOString(),
      remediation: severity !== 'info' ? `To fix the ${title.toLowerCase()}, update security configurations and patch affected systems.` : undefined,
    };
  });
};

// Generate mock scan requests
export const generateMockScanRequests = (count = randomInt(0, 0)): ScanRequest[] => {
  const statuses: Array<ScanRequest['status']> = ['queued', 'running', 'completed', 'failed'];
  
  return Array.from({ length: count }, (_, i) => {
    const timestamp = new Date(Date.now() - randomInt(0, 7 * 24 * 60 * 60 * 1000)).toISOString();
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    
    return {
      requestId: `scan-${Date.now()}-${i}`,
      timestamp,
      status,
      config: {
        targets: ['example.com', 'test-site.org'],
        batches: randomInt(1, 5),
        threads: randomInt(1, 10),
        output: 's3'
      }
    };
  });
};

// Generate mock scan results
export const generateMockScanResults = (scanRequests: ScanRequest[]): ScanResult[] => {
  return scanRequests
    .filter(scan => scan.status === 'completed')
    .map(scan => {
      const vulnerabilities = generateMockVulnerabilities();
      
      const criticalCount = vulnerabilities.filter(v => v.severity === 'critical').length;
      const highCount = vulnerabilities.filter(v => v.severity === 'high').length;
      const mediumCount = vulnerabilities.filter(v => v.severity === 'medium').length;
      const lowCount = vulnerabilities.filter(v => v.severity === 'low').length;
      const infoCount = vulnerabilities.filter(v => v.severity === 'info').length;
      
      return {
        requestId: scan.requestId,
        timestamp: scan.timestamp,
        status: 'completed',
        summary: {
          totalVulnerabilities: vulnerabilities.length,
          criticalCount,
          highCount,
          mediumCount,
          lowCount,
          infoCount
        },
        vulnerabilities
      };
    });
};

// Generate mock dashboard metrics
export const generateMockDashboardMetrics = () => {
  const recentScans = generateMockScanRequests();
  const activeScanCount = recentScans.filter(scan => scan.status === 'running').length;

  
  return {
    totalScans: 0,
    activeScanCount,

    recentScans
  };
};
