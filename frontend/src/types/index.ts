
// Authentication types
export interface User {
  id: string;
  email: string;
  name: string;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// Scan types
export interface ScanTarget {
  domain: string;
}

export interface ScanConfig {
  targets: string[];
  batches: number;
  threads: number;
  output: 's3'
}

export interface ScanRequest {
  requestId: string;
  timestamp: string;
  status: 'queued' | 'running' | 'completed' | 'failed';
  config: ScanConfig;
}

export interface Vulnerability {
  id: string;
  target: string;
  title: string;
  description: string;
  severity: 'critical' | 'high' | 'medium' | 'low' | 'info';
  discoveredAt: string;
  remediation?: string;
}

export interface ScanResult {
  requestId: string;
  timestamp: string;
  status: 'completed' | 'failed';
  summary: {
    totalVulnerabilities: number;
    criticalCount: number;
    highCount: number;
    mediumCount: number;
    lowCount: number;
    infoCount: number;
  };
  vulnerabilities: Vulnerability[];
}

