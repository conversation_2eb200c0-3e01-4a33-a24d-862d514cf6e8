# Terraform Modular Restructuring - Nuclei Lambda Module

## Overview

This document describes the modular restructuring of the Nuclei scanner Lambda function infrastructure. The restructuring separates the Lambda function and its dependencies into a dedicated `nuclei_lambda` module, following the established patterns used by other modules in the project.

## Background

Previously, the Nuclei Lambda function infrastructure was mixed between the root `terraform/main.tf` and various supporting files. This created several issues:

1. **Lack of modularity**: Lambda resources couldn't be deployed independently
2. **Inconsistent patterns**: Other modules (frontend, network, pow) were properly modularized
3. **Dependency confusion**: Lambda dependencies were scattered across multiple files
4. **Maintenance complexity**: Changes to Lambda infrastructure required editing multiple files

## Solution: Nuclei Lambda Module

The solution creates a new `terraform/nuclei_lambda/` module that encapsulates all Lambda-related infrastructure:

### Module Structure

```
terraform/nuclei_lambda/
├── main.tf              # Lambda function, layers, and Glue catalog
├── variables.tf         # Input variables with validation
├── outputs.tf          # Output values for other modules
├── iam.tf              # IAM roles and policies
├── s3.tf               # S3 bucket and lifecycle policies
├── artifacts.tf        # Nuclei binary and template management
├── cloudwatch.tf       # CloudWatch logs, alarms, and dashboards
├── terraform.tf        # Provider requirements
└── README.md           # Comprehensive module documentation
```

### Key Components

#### 1. Lambda Function and Layers
- **Main Lambda Function**: Nuclei scanner execution environment
- **Nuclei Binary Layer**: Contains the Nuclei scanner binary
- **Templates Layer**: Custom Nuclei templates
- **Config Layer**: Nuclei configuration files

#### 2. S3 Storage
- **Artifacts Bucket**: Stores Nuclei binary, templates, and configurations
- **Findings Storage**: Stores scan results with lifecycle policies
- **Security**: Server-side encryption, public access blocked

#### 3. IAM Security
- **Least Privilege**: Minimal required permissions
- **Scoped Access**: S3 access limited to specific paths
- **Optional VPC Support**: For Lambda functions in VPC

#### 4. Monitoring (Optional)
- **CloudWatch Logs**: Configurable retention
- **CloudWatch Alarms**: Error rate, duration, throttle monitoring
- **CloudWatch Dashboard**: Unified monitoring view
- **Lambda Insights**: Enhanced performance monitoring

#### 5. Data Catalog
- **AWS Glue Database**: For scan findings analysis
- **Glue Table**: Structured schema for findings data

## Migration Changes

### Files Created

1. **New Module Files**:
   - `terraform/nuclei_lambda/main.tf`
   - `terraform/nuclei_lambda/variables.tf`
   - `terraform/nuclei_lambda/outputs.tf`
   - `terraform/nuclei_lambda/iam.tf`
   - `terraform/nuclei_lambda/s3.tf`
   - `terraform/nuclei_lambda/artifacts.tf`
   - `terraform/nuclei_lambda/cloudwatch.tf`
   - `terraform/nuclei_lambda/terraform.tf`
   - `terraform/nuclei_lambda/README.md`

2. **Documentation**:
   - `docs/terraform-modular-restructuring.md` (this file)

### Files Modified

1. **Root Configuration**:
   - `terraform/main.tf`: Replaced Lambda resources with module call
   - `terraform/variables.tf`: Added nuclei_lambda_environment variable
   - `terraform/output.tf`: Updated outputs to use new module

### Files Removed

1. **Consolidated into Module**:
   - `terraform/artifacts.tf` → `terraform/nuclei_lambda/artifacts.tf`
   - `terraform/bucket.tf` → `terraform/nuclei_lambda/s3.tf`
   - `terraform/glue.tf` → `terraform/nuclei_lambda/main.tf`

## Usage Examples

### Basic Usage

```hcl
module "nuclei_lambda" {
  source = "./nuclei_lambda"

  # Required variables
  project_name = "fast-scan"
  environment  = "dev"

  # Nuclei configuration
  nuclei_version = "3.1.7"
  nuclei_timeout = 900
  memory_size    = 512

  tags = {
    Project     = "fast-scan"
    Environment = "dev"
  }
}
```

### Production Configuration

```hcl
module "nuclei_lambda" {
  source = "./nuclei_lambda"

  project_name = "fast-scan"
  environment  = "prod"

  # Enhanced configuration
  nuclei_version = "3.1.7"
  nuclei_timeout = 900
  memory_size    = 1024

  # Storage configuration
  enable_s3_versioning    = true
  findings_retention_days = 365

  # Monitoring configuration
  log_retention_days         = 90
  enable_cloudwatch_alarms   = true
  enable_cloudwatch_dashboard = true
  enable_lambda_insights     = true

  tags = {
    Project     = "fast-scan"
    Environment = "prod"
    CostCenter  = "security"
  }
}
```

### Integration with Backend

```hcl
module "nuclei_lambda" {
  source = "./nuclei_lambda"
  
  project_name = "fast-scan"
  environment  = "dev"
  # ... other configuration
}

module "nuclear_pond_backend" {
  source = "./nuclear_pond_backend"
  
  # Pass Lambda details to backend
  lambda_function_name = module.nuclei_lambda.lambda_function_name
  lambda_function_arn  = module.nuclei_lambda.lambda_function_arn
  
  # ... other configuration
}
```

## Benefits

### 1. Independent Deployment
- Deploy/destroy Lambda infrastructure independently
- Faster iteration during development
- Reduced blast radius for changes

### 2. Consistent Patterns
- Follows same structure as other modules
- Standardized variable naming and validation
- Consistent documentation format

### 3. Improved Maintainability
- Clear separation of concerns
- Self-contained module with all dependencies
- Easier testing and validation

### 4. Enhanced Security
- Least-privilege IAM policies
- Scoped S3 access permissions
- Optional VPC support

### 5. Better Monitoring
- Optional CloudWatch alarms and dashboards
- Configurable log retention
- Lambda Insights support

## Deployment Instructions

### 1. Initial Deployment

```bash
# Navigate to terraform directory
cd terraform

# Initialize Terraform (downloads new module)
terraform init

# Plan the deployment
terraform plan

# Apply the changes
terraform apply
```

### 2. Targeted Deployment

```bash
# Deploy only the nuclei_lambda module
terraform apply -target=module.nuclei_lambda

# Deploy nuclei_lambda and dependent backend
terraform apply -target=module.nuclei_lambda -target=module.nuclear_pond_backend
```

### 3. Module-specific Operations

```bash
# Plan changes to nuclei_lambda module only
terraform plan -target=module.nuclei_lambda

# Destroy nuclei_lambda module (will affect dependent modules)
terraform destroy -target=module.nuclei_lambda
```

## Configuration Variables

### Required Variables
- `project_name`: Project identifier for resource naming
- `environment`: Deployment environment (dev/staging/prod)

### Key Optional Variables
- `nuclei_version`: Nuclei scanner version (default: "3.1.7")
- `nuclei_timeout`: Lambda timeout in seconds (default: 900)
- `memory_size`: Lambda memory in MB (default: 512)
- `log_retention_days`: CloudWatch log retention (default: 30)
- `findings_retention_days`: S3 findings retention (default: 90)

For a complete list, see `terraform/nuclei_lambda/variables.tf`.

## Outputs

The module provides comprehensive outputs for integration with other modules:

### Key Outputs
- `lambda_function_name`: For backend integration
- `lambda_function_arn`: For IAM policies and triggers
- `s3_bucket_name`: For findings storage
- `findings_s3_path`: For data analysis tools

For a complete list, see `terraform/nuclei_lambda/outputs.tf`.

## Backward Compatibility

The restructuring maintains backward compatibility:

1. **Legacy Outputs**: Root outputs still provide Lambda function details
2. **Existing Integrations**: Backend module integration unchanged
3. **Configuration**: Existing variable names preserved where possible

## Future Enhancements

The modular structure enables future enhancements:

1. **Multi-Environment Support**: Easy deployment to multiple environments
2. **Advanced Monitoring**: Enhanced CloudWatch integration
3. **Performance Optimization**: Independent Lambda tuning
4. **Security Hardening**: VPC deployment, enhanced IAM policies
5. **Cost Optimization**: Advanced S3 lifecycle policies

## Troubleshooting

### Common Issues

1. **Module Not Found**: Run `terraform init` after adding the module
2. **Permission Errors**: Verify IAM permissions for module resources
3. **State Conflicts**: Use `terraform import` for existing resources if needed

### Useful Commands

```bash
# Validate module configuration
terraform validate

# Check module formatting
terraform fmt -recursive

# View module documentation
terraform-docs markdown terraform/nuclei_lambda/
```

## Related Documentation

- [Nuclei Lambda Module README](../terraform/nuclei_lambda/README.md)
- [Nuclear Pond Backend Module](../terraform/nuclear_pond_backend/README.md)
- [Network Module](../terraform/network/README.md)
- [Frontend Module](../terraform/frontend/README.md)
