# ==============================================================================
# NETWORK MODULE VARIABLES
# ==============================================================================

variable "project_name" {
  description = "Name of the project used for resource naming"
  type        = string
}

variable "tags" {
  description = "Common tags to apply to all resources"
  type        = map(string)
  default     = {}
}

# VPC Configuration
variable "vpc_cidr" {
  description = "CIDR block for the VPC"
  type        = string
  default     = "10.0.0.0/16"
}

# Public Subnet Configuration
variable "public_subnet_az1_cidr" {
  description = "CIDR block for public subnet in AZ1"
  type        = string
  default     = "********/24"
}

variable "public_subnet_az2_cidr" {
  description = "CIDR block for public subnet in AZ2"
  type        = string
  default     = "********/24"
}

# Private Subnet Configuration
variable "private_subnet_az1_cidr" {
  description = "CIDR block for private subnet in AZ1"
  type        = string
  default     = "********/24"
}

variable "private_subnet_az2_cidr" {
  description = "CIDR block for private subnet in AZ2"
  type        = string
  default     = "********/24"
}

# High Availability Configuration
variable "enable_nat_gateway_ha" {
  description = "Whether to create a second NAT Gateway in AZ2 for high availability"
  type        = bool
  default     = false
}
