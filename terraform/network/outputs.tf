# ==============================================================================
# NETWORK MODULE OUTPUTS
# ==============================================================================

# VPC Outputs
output "vpc_id" {
  description = "ID of the VPC"
  value       = aws_vpc.main.id
}

output "vpc_cidr_block" {
  description = "CIDR block of the VPC"
  value       = aws_vpc.main.cidr_block
}

# Public Subnet Outputs
output "public_subnet_ids" {
  description = "List of public subnet IDs"
  value       = [aws_subnet.public_subnet_az1.id, aws_subnet.public_subnet_az2.id]
}

output "public_subnet_az1_id" {
  description = "ID of public subnet in AZ1"
  value       = aws_subnet.public_subnet_az1.id
}

output "public_subnet_az2_id" {
  description = "ID of public subnet in AZ2"
  value       = aws_subnet.public_subnet_az2.id
}

# Private Subnet Outputs
output "private_subnet_ids" {
  description = "List of private subnet IDs"
  value       = [aws_subnet.private_subnet_az1.id, aws_subnet.private_subnet_az2.id]
}

output "private_subnet_az1_id" {
  description = "ID of private subnet in AZ1"
  value       = aws_subnet.private_subnet_az1.id
}

output "private_subnet_az2_id" {
  description = "ID of private subnet in AZ2"
  value       = aws_subnet.private_subnet_az2.id
}

# Gateway Outputs
output "internet_gateway_id" {
  description = "ID of the Internet Gateway"
  value       = aws_internet_gateway.main.id
}

output "nat_gateway_id" {
  description = "ID of the NAT Gateway"
  value       = aws_nat_gateway.nat_gw_az1.id
}

output "nat_gateway_public_ip" {
  description = "Public IP of the NAT Gateway"
  value       = aws_eip.nat_eip_az1.public_ip
}

# Availability Zone Outputs
output "availability_zones" {
  description = "List of availability zones used"
  value       = [
    data.aws_availability_zones.available.names[0],
    data.aws_availability_zones.available.names[1]
  ]
}
