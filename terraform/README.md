# Nuclear Pond Infrastructure

![Infrastructure](/assets/infrastructure.png)

This directory contains the **modular Terraform configuration** for deploying the complete Nuclear Pond infrastructure on AWS. The infrastructure includes Lambda functions for running Nuclei scans, a containerized Nuclear Pond backend service, optional frontend deployment, and demo targets for testing.

## 🏗️ Modular Architecture

The infrastructure is now organized into **independent, reusable modules**:

- **🌐 Network Module** (`./network/`): Shared VPC, subnets, and networking
- **🐳 Nuclear Pond Backend** (`./nuclear_pond_backend/`): ECS Fargate service with ALB
- **🎯 PoW Targets** (`./pow/`): Demo infrastructure for testing scans
- **🖥️ Frontend** (`./frontend/`): Web interface with S3 and CloudFront
- **⚡ Lambda Functions**: Core Nuclei execution engine


## 🚀 Quick Start

### Prerequisites
- AWS CLI configured with appropriate permissions
- Terraform >= 1.0
- Docker (for building container images)

### Basic Deployment

```bash
# 1. Navigate to terraform directory
cd terraform

# 2. Initialize Terraform
terraform init

# 3. Configure variables (create terraform.tfvars)
cat > terraform.tfvars << EOF
project_name = "your-project-name"
nuclearpond_api_key = "your-secure-api-key"

# Enable Nuclear Pond backend service
enable_nuclear_pond_backend = true
nuclear_pond_environment = "dev"

# Optional: Enable additional components
enable_pow_targets = true
enable_frontend_deployment = true
EOF

# 4. Deploy infrastructure
terraform apply

# 5. Deploy the Nuclear Pond application
cd nuclear_pond_backend
./deploy-backend-dev.sh latest
```

### Independent Module Deployment

```bash
# Deploy only specific modules
terraform apply -target=module.network
terraform apply -target=module.nuclear_pond_backend
terraform apply -target=module.frontend
```

## 📚 Module Documentation

- **[Nuclear Pond Backend](./nuclear_pond_backend/README.md)** - ECS service deployment
- **[Network Module](./network/README.md)** - Shared networking infrastructure
- **[PoW Targets Module](./pow/README.md)** - Demo infrastructure for testing scans (details in module README)
- **[Migration Guide](./MIGRATION_GUIDE.md)** - Upgrade from monolithic setup
- **[Deployment Guide](./nuclear_pond_backend/DEPLOYMENT.md)** - Detailed deployment instructions for the backend

---

## 🔧 Core Lambda Function (Nuclei Runner)

The heart of this infrastructure is an AWS Lambda function designed to execute [Nuclei](https://github.com/projectdiscovery/nuclei) scans. This function acts as the backend execution engine for the [Nuclear Pond](../nuclear_pond/) application, enabling dynamic and scalable security scanning.

Nuclei is a powerful tool for identifying known vulnerabilities, misconfigurations, and other security issues. This Lambda integration allows for continuous monitoring within your cloud environment.

> :warning: **Security Note**: This Lambda function is designed for flexibility and passes arguments directly to Nuclei. This means it is vulnerable to Remote Code Execution if input is not carefully sanitized. Deploy with caution, especially if network interfaces are attached to the Lambda.

Key considerations for the Lambda:
- Avoid passing `-u`, `-l`, `-json`, or `-o` flags directly; the Lambda handles these. Other Nuclei arguments can be used.
- `HOME` is set to `/tmp` due to Lambda's read-only filesystem, which might affect warm starts if Nuclei writes to `$HOME/.config`.
- The Go Lambda binary is rebuilt on each `terraform apply` for ease of development.
- Updates to configuration files might require infrastructure destruction and recreation.

### Event JSON for Lambda Invocation

The Lambda function expects a JSON payload with the following structure:

```json
{
  "Targets": [
    "https://devsecopsdocs.com"
  ],
  "Args": [
    "-t",
    "dns"
  ],
  "Output": "json" // Options: "json", "s3", "cli" (command line output)
}
```
- `Targets`: A list of one or more targets. The Lambda adapts by using `-u` (single target) or `-l` (list of targets via a temporary file).
- `Args`: Any valid Nuclei flags (excluding those managed by the Lambda).
- `Output`: Specifies the desired output format. "json" returns JSON findings, "s3" indicates results are uploaded to S3 (key is returned), "cli" returns raw command-line output.

## Engineering Decisions Overview

- **Flexibility vs. Security**: Args are passed directly to Nuclei in the Lambda for maximum flexibility, accepting the RCE risk.
- **Statelessness**: Lambda is generally stateless, but Nuclei's use of `$HOME/.config` is managed by redirecting `HOME` to `/tmp`.
- **Development Velocity**: Lambda is rebuilt on every apply to simplify iteration.

## Infrastructure Summary (Root Module Resources):

This Terraform configuration provisions core AWS resources for the Nuclei scanning platform:

*   **AWS Lambda Function & Layers**: The primary Nuclei execution environment, packaged with necessary binaries, templates, and configurations via Lambda Layers.
*   **AWS S3 Bucket**: Stores Lambda deployment packages, Nuclei assets, and scan findings. Configured for encryption and blocked public access.
*   **AWS IAM Roles and Policies**: Provides the Lambda function with least-privilege permissions for CloudWatch Logs and S3 (for findings).
*   **AWS CloudWatch Log Group**: Centralized logging for the Lambda function.
*   **AWS DynamoDB Table**: Manages scan state, using `scan_id` as the primary key and TTL for record expiration.
*   **AWS Glue Catalog Database and Table**: Enables querying of Nuclei JSON findings stored in S3 using services like Athena. Defines a schema and partitions for efficient data access.
*   **Proof of Work (PoW) Target Infrastructure**: Deployed via the `pow/` module (see `terraform/pow/README.md` for details). This optional module creates demo targets for testing Nuclei scans.

*   **Terraform Versions and Providers** (`versions.tf`):
    *   Specifies required Terraform version `>= 1.0` and providers: `aws` (`4.50.0`), `null` (`3.2.1`), `archive` (`2.2.0`), and `github` (`5.14.0`).

<!-- BEGIN_TF_DOCS -->
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.0 |
| <a name="requirement_archive"></a> [archive](#requirement\_archive) | 2.2.0 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | 4.50.0 |
| <a name="requirement_github"></a> [github](#requirement\_github) | 5.14.0 |
| <a name="requirement_null"></a> [null](#requirement\_null) | 3.2.1 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_archive"></a> [archive](#provider\_archive) | 2.2.0 |
| <a name="provider_aws"></a> [aws](#provider\_aws) | 4.50.0 |
| <a name="provider_github"></a> [github](#provider\_github) | 5.14.0 |
| <a name="provider_null"></a> [null](#provider\_null) | 3.2.1 |

## Modules

| Name | Source | Description |
|------|--------|-------------|
| <a name="module_pow_targets"></a> [pow_targets](#module\_pow\_targets) | ./pow | Optional Proof of Work (PoW) target infrastructure for testing Nuclei scans |

## Resources

| Name | Type |
|------|------|
| [aws_cloudwatch_log_group.log_group](https://registry.terraform.io/providers/hashicorp/aws/4.50.0/docs/resources/cloudwatch_log_group) | resource |
| [aws_dynamodb_table.scan_state_table](https://registry.terraform.io/providers/hashicorp/aws/4.50.0/docs/resources/dynamodb_table) | resource |
| [aws_glue_catalog_database.database](https://registry.terraform.io/providers/hashicorp/aws/4.50.0/docs/resources/glue_catalog_database) | resource |
| [aws_glue_catalog_table.table](https://registry.terraform.io/providers/hashicorp/aws/4.50.0/docs/resources/glue_catalog_table) | resource |
| [aws_iam_policy.policy](https://registry.terraform.io/providers/hashicorp/aws/4.50.0/docs/resources/iam_policy) | resource |
| [aws_iam_role.lambda_role](https://registry.terraform.io/providers/hashicorp/aws/4.50.0/docs/resources/iam_role) | resource |
| [aws_iam_role_policy_attachment.policy](https://registry.terraform.io/providers/hashicorp/aws/4.50.0/docs/resources/iam_role_policy_attachment) | resource |
| [aws_lambda_alias.alias](https://registry.terraform.io/providers/hashicorp/aws/4.50.0/docs/resources/lambda_alias) | resource |
| [aws_lambda_function.function](https://registry.terraform.io/providers/hashicorp/aws/4.50.0/docs/resources/lambda_function) | resource |
| [aws_lambda_layer_version.configs_layer](https://registry.terraform.io/providers/hashicorp/aws/4.50.0/docs/resources/lambda_layer_version) | resource |
| [aws_lambda_layer_version.layer](https://registry.terraform.io/providers/hashicorp/aws/4.50.0/docs/resources/lambda_layer_version) | resource |
| [aws_lambda_layer_version.templates_layer](https://registry.terraform.io/providers/hashicorp/aws/4.50.0/docs/resources/lambda_layer_version) | resource |
| [aws_s3_bucket.bucket](https://registry.terraform.io/providers/hashicorp/aws/4.50.0/docs/resources/s3_bucket) | resource |
| [aws_s3_bucket_public_access_block.block](https://registry.terraform.io/providers/hashicorp/aws/4.50.0/docs/resources/s3_bucket_public_access_block) | resource |
| [aws_s3_bucket_server_side_encryption_configuration.encryption](https://registry.terraform.io/providers/hashicorp/aws/4.50.0/docs/resources/s3_bucket_server_side_encryption_configuration) | resource |
| [aws_s3_object.upload_config](https://registry.terraform.io/providers/hashicorp/aws/4.50.0/docs/resources/s3_object) | resource |
| [aws_s3_object.upload_nuclei](https://registry.terraform.io/providers/hashicorp/aws/4.50.0/docs/resources/s3_object) | resource |
| [aws_s3_object.upload_templates](https://registry.terraform.io/providers/hashicorp/aws/4.50.0/docs/resources/s3_object) | resource |
| [null_resource.build](https://registry.terraform.io/providers/hashicorp/null/3.2.1/docs/resources/resource) | resource |
| [null_resource.download_nuclei](https://registry.terraform.io/providers/hashicorp/null/3.2.1/docs/resources/resource) | resource |
| [null_resource.download_templates](https://registry.terraform.io/providers/hashicorp/null/3.2.1/docs/resources/resource) | resource |
| [archive_file.nuclei_config](https://registry.terraform.io/providers/hashicorp/archive/2.2.0/docs/data-sources/file) | data source |
| [archive_file.zip](https://registry.terraform.io/providers/hashicorp/archive/2.2.0/docs/data-sources/file) | data source |
| [aws_iam_policy_document.policy](https://registry.terraform.io/providers/hashicorp/aws/4.50.0/docs/data-sources/iam_policy_document) | data source |
| [aws_iam_policy_document.trust](https://registry.terraform.io/providers/hashicorp/aws/4.50.0/docs/data-sources/iam_policy_document) | data source |
| [github_release.templates](https://registry.terraform.io/providers/hashicorp/github/5.14.0/docs/data-sources/release) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_enable_pow_targets"></a> [enable\_pow\_targets](#input\_enable\_pow\_targets) | Whether to create PoW demo target infrastructure | `bool` | `false` | no |
| <a name="input_memory_size"></a> [memory\_size](#input\_memory\_size) | n/a | `number` | `512` | no |
| <a name="input_nuclearpond_api_key"></a> [nuclearpond\_api\_key](#input\_nuclearpond\_api\_key) | API key for the Nuclear Pond service | `string` | n/a | yes |
| <a name="input_nuclei_arch"></a> [nuclei\_arch](#input\_nuclei\_arch) | Nuclei architecture to use | `string` | `"linux_amd64"` | no |
| <a name="input_nuclei_timeout"></a> [nuclei\_timeout](#input\_nuclei\_timeout) | Lambda function timeout | `number` | `900` | no |
| <a name="input_nuclei_version"></a> [nuclei\_version](#input\_nuclei\_version) | Nuclei version to use | `string` | `"2.8.7"` | no |
| <a name="input_pow_domain_name"></a> [pow\_domain\_name](#input\_pow\_domain\_name) | The domain name to use for the PoW targets | `string` | `""` | no |
| <a name="input_project_name"></a> [project\_name](#input\_project\_name) | Name of the project to create and must be unique as S3 bucket names are global | `any` | n/a | yes |
| <a name="input_tags"></a> [tags](#input\_tags) | n/a | `map(string)` | <pre>{<br>  "Name": "nuclei-scanner"<br>}</pre> | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_dynamodb_state_table"></a> [dynamodb\_state\_table](#output\_dynamodb\_state\_table) | n/a |
| <a name="output_function_name"></a> [function\_name](#output\_function\_name) | n/a |
| <a name="output_pow_domain"></a> [pow\_domain](#output\_pow\_domain) | The domain name used for PoW targets |
| <a name="output_pow_enabled"></a> [pow\_enabled](#output\_pow\_enabled) | Whether the PoW infrastructure is enabled |
| <a name="output_pow_example_urls"></a> [pow\_example\_urls](#output\_pow\_example\_urls) | Example URLs for Nuclei scanning (after DNS is configured) |
| <a name="output_pow_name_servers"></a> [pow\_name\_servers](#output\_pow\_name\_servers) | Name servers for the PoW domain (update these at your domain registrar) |
| <a name="output_pow_target_alb"></a> [pow\_target\_alb](#output\_pow\_target\_alb) | ALB DNS name for PoW targets |
<!-- END_TF_DOCS -->