# Application Load Balancer configuration for PoW targets

# Security Group for the PoW ALB
resource "aws_security_group" "pow_alb_sg" {
  count = local.create_pow_resources
  
  name        = "${var.project_name}-pow-alb-sg"
  description = "Allow HTTP inbound traffic to PoW ALB"
  vpc_id      = var.vpc_id

  ingress {
    description = "HTTP from Internet"
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
  
  tags = merge(var.tags, {
    Name = "${var.project_name}-pow-alb-sg"
  })
}

# PoW Application Load Balancer
resource "aws_lb" "pow_alb" {
  count = local.create_pow_resources
  
  name               = "${var.project_name}-pow-alb"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [aws_security_group.pow_alb_sg[0].id]
  subnets            = var.public_subnet_ids

  enable_deletion_protection = false
  
  tags = merge(var.tags, {
    Name = "${var.project_name}-pow-alb"
  })
}

# Target Group for PoW EC2 instances
resource "aws_lb_target_group" "pow_ec2_tg" {
  count = local.create_pow_resources
  
  name        = "${var.project_name}-pow-ec2-tg"
  port        = 80
  protocol    = "HTTP"
  target_type = "instance"
  vpc_id      = var.vpc_id

  health_check {
    enabled             = true
    path                = "/"
    protocol            = "HTTP"
    matcher             = "200"
    interval            = 30
    timeout             = 5
    healthy_threshold   = 2
    unhealthy_threshold = 2
  }
  
  tags = merge(var.tags, {
    Name = "${var.project_name}-pow-ec2-tg"
  })
}

# Listener for the PoW ALB
resource "aws_lb_listener" "pow_http_listener" {
  count = local.create_pow_resources
  
  load_balancer_arn = aws_lb.pow_alb[0].arn
  port              = "80"
  protocol          = "HTTP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.pow_ec2_tg[0].arn
  }
}

# Output the ALB DNS name
output "pow_alb_dns_name" {
  description = "DNS name of the PoW ALB"
  value       = local.create_pow_resources > 0 ? aws_lb.pow_alb[0].dns_name : null
} 