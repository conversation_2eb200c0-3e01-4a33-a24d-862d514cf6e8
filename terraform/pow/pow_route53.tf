# Route53 DNS configuration for PoW targets

# Use the existing hosted zone created automatically when purchasing the domain through Route53
data "aws_route53_zone" "pow_zone" {
  count = local.create_pow_resources
  
  # Use the exact domain name that was registered through Route53
  name         = var.pow_domain_name
  private_zone = false
}

# Wildcard DNS record pointing to the ALB
resource "aws_route53_record" "pow_wildcard_dns" {
  count = local.create_pow_resources
  
  zone_id = data.aws_route53_zone.pow_zone[0].zone_id
  name    = "*.${var.pow_domain_name}"
  type    = "A"

  alias {
    name                   = aws_lb.pow_alb[0].dns_name
    zone_id                = aws_lb.pow_alb[0].zone_id
    evaluate_target_health = true
  }
}

# Also add a root domain record pointing to the ALB
resource "aws_route53_record" "pow_root_dns" {
  count = local.create_pow_resources
  
  zone_id = data.aws_route53_zone.pow_zone[0].zone_id
  name    = var.pow_domain_name
  type    = "A"

  alias {
    name                   = aws_lb.pow_alb[0].dns_name
    zone_id                = aws_lb.pow_alb[0].zone_id
    evaluate_target_health = true
  }
}

# Output the hosted zone ID (useful for reference)
output "pow_zone_id" {
  description = "The Route53 hosted zone ID for the PoW domain"
  value       = local.create_pow_resources > 0 ? data.aws_route53_zone.pow_zone[0].zone_id : null
} 