variable "project_name" {
  description = "Name of the project, used for resource naming"
  type        = string
}

variable "tags" {
  description = "Common tags to apply to all resources"
  type        = map(string)
  default     = {}
}

variable "enable_pow_targets" {
  description = "Whether to create PoW demo target infrastructure"
  type        = bool
  default     = false
}

variable "pow_domain_name" {
  description = "The domain name to use for the PoW targets (e.g., fast-scan-pow.com)"
  type        = string
  default     = ""
}

variable "vpc_id" {
  description = "ID of the VPC where resources will be created"
  type        = string
}

variable "public_subnet_ids" {
  description = "IDs of public subnets where ALB and EC2 instances will be placed"
  type        = list(string)
}

variable "pow_instance_type" {
  description = "EC2 instance type for the PoW targets"
  type        = string
  default     = "t3.micro"
}

variable "pow_key_name" {
  description = "SSH key pair name to use for the EC2 instances (optional)"
  type        = string
  default     = null
} 