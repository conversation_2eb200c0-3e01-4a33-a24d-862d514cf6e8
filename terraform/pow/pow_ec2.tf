# EC2 instances configuration for PoW targets

# Security Group for the PoW EC2 instances
resource "aws_security_group" "pow_ec2_sg" {
  count = local.create_pow_resources
  
  name        = "${var.project_name}-pow-ec2-sg"
  description = "Allow HTTP inbound traffic from PoW ALB to EC2 instances"
  vpc_id      = var.vpc_id

  ingress {
    description     = "HTTP from PoW ALB"
    from_port       = 80
    to_port         = 80
    protocol        = "tcp"
    security_groups = [aws_security_group.pow_alb_sg[0].id]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"] # Allow all outbound for updates, etc.
  }
  
  tags = merge(var.tags, {
    Name = "${var.project_name}-pow-ec2-sg"
  })
}

# Latest Amazon Linux 2 AMI
data "aws_ami" "amazon_linux_2" {
  most_recent = true
  owners      = ["amazon"]

  filter {
    name   = "name"
    values = ["amzn2-ami-hvm-*-x86_64-gp2"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }
}

# User Data script to set up a simple Nginx server
locals {
  user_data = <<-EOF
    #!/bin/bash
    sudo yum update -y
    sudo amazon-linux-extras install nginx1 -y
    sudo systemctl start nginx
    sudo systemctl enable nginx
    
    # Get instance metadata
    HOSTNAME=$(hostname -f)
    INSTANCE_ID=$(curl -s http://***************/latest/meta-data/instance-id)
    AZ=$(curl -s http://***************/latest/meta-data/placement/availability-zone)
    
    # Create the HTML file with actual values
    cat > /usr/share/nginx/html/index.html << 'HTML'
<!DOCTYPE html>
<html>
<head>
  <title>PoW Target</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 0; padding: 20px; color: #333; }
    h1 { color: #0066cc; }
    .container { max-width: 800px; margin: 0 auto; background: #f9f9f9; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
  </style>
</head>
<body>
  <div class="container">
    <h1>Nuclear Pond - PoW Target</h1>
    <p>Hello from HOSTNAME_PLACEHOLDER</p>
    <p>This is a test target for Nuclei vulnerability scanning.</p>
    <p>Instance ID: <code>INSTANCE_ID_PLACEHOLDER</code></p>
    <p>Availability Zone: <code>AZ_PLACEHOLDER</code></p>
  </div>
</body>
</html>
HTML

    # Replace placeholders with actual values
    sudo sed -i "s/HOSTNAME_PLACEHOLDER/$HOSTNAME/g" /usr/share/nginx/html/index.html
    sudo sed -i "s/INSTANCE_ID_PLACEHOLDER/$INSTANCE_ID/g" /usr/share/nginx/html/index.html
    sudo sed -i "s/AZ_PLACEHOLDER/$AZ/g" /usr/share/nginx/html/index.html
    EOF
}

# First EC2 instance
resource "aws_instance" "pow_target_ec2_1" {
  count = local.create_pow_resources
  
  ami                    = data.aws_ami.amazon_linux_2.id
  instance_type          = var.pow_instance_type
  subnet_id              = var.public_subnet_ids[0]
  vpc_security_group_ids = [aws_security_group.pow_ec2_sg[0].id]
  user_data              = local.user_data
  key_name               = var.pow_key_name

  tags = merge(var.tags, {
    Name = "${var.project_name}-pow-target-1"
  })
}

# Second EC2 instance in a different AZ for high availability
resource "aws_instance" "pow_target_ec2_2" {
  count = local.create_pow_resources
  
  ami                    = data.aws_ami.amazon_linux_2.id
  instance_type          = var.pow_instance_type
  subnet_id              = length(var.public_subnet_ids) > 1 ? var.public_subnet_ids[1] : var.public_subnet_ids[0]
  vpc_security_group_ids = [aws_security_group.pow_ec2_sg[0].id]
  user_data              = local.user_data
  key_name               = var.pow_key_name

  tags = merge(var.tags, {
    Name = "${var.project_name}-pow-target-2"
  })
}

# Attach instances to the Target Group
resource "aws_lb_target_group_attachment" "pow_attach_ec2_1" {
  count = local.create_pow_resources
  
  target_group_arn = aws_lb_target_group.pow_ec2_tg[0].arn
  target_id        = aws_instance.pow_target_ec2_1[0].id
  port             = 80
}

resource "aws_lb_target_group_attachment" "pow_attach_ec2_2" {
  count = local.create_pow_resources
  
  target_group_arn = aws_lb_target_group.pow_ec2_tg[0].arn
  target_id        = aws_instance.pow_target_ec2_2[0].id
  port             = 80
}

# Output the EC2 instance IDs
output "pow_ec2_instance_ids" {
  description = "IDs of the PoW EC2 instances"
  value       = local.create_pow_resources > 0 ? [
    aws_instance.pow_target_ec2_1[0].id,
    aws_instance.pow_target_ec2_2[0].id
  ] : null
} 