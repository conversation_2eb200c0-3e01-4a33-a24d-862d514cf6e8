# ==============================================================================
# TERRAFORM CONFIGURATION FOR NUCLEI LAMBDA MODULE
# ==============================================================================

terraform {
  required_version = ">= 1.0"

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = ">= 4.50.0"
    }
    
    null = {
      source  = "hashicorp/null"
      version = ">= 3.0"
    }
    
    archive = {
      source  = "hashicorp/archive"
      version = ">= 2.0"
    }
    
    local = {
      source  = "hashicorp/local"
      version = ">= 2.0"
    }
  }
}
