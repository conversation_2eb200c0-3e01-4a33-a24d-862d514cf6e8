# ==============================================================================
# NUCLEI LAMBDA MODULE
# ==============================================================================
# This module deploys the Nuclei scanner Lambda function with all required
# infrastructure including S3 bucket, Lambda layers, IAM roles, and artifacts.

# Data sources for AWS information
data "aws_region" "current" {}
data "aws_caller_identity" "current" {}

# ==============================================================================
# LAMBDA FUNCTION AND LAYERS
# ==============================================================================

# Main Nuclei scanner Lambda function
# tfsec:ignore:aws-lambda-enable-tracing
resource "aws_lambda_function" "nuclei_function" {
  depends_on    = [aws_lambda_layer_version.nuclei_layer, aws_lambda_layer_version.templates_layer, aws_lambda_layer_version.configs_layer, data.archive_file.lambda_zip]
  filename      = data.archive_file.lambda_zip.output_path
  function_name = "${var.project_name}-nuclei-function"

  role   = aws_iam_role.lambda_role.arn
  layers = [aws_lambda_layer_version.nuclei_layer.arn, aws_lambda_layer_version.templates_layer.arn, aws_lambda_layer_version.configs_layer.arn]

  handler     = "bootstrap"
  runtime     = var.lambda_runtime
  timeout     = var.nuclei_timeout
  memory_size = var.memory_size

  # Reserved concurrency configuration
  reserved_concurrent_executions = var.reserved_concurrent_executions >= 0 ? var.reserved_concurrent_executions : null

  source_code_hash = data.archive_file.lambda_zip.output_base64sha256

  environment {
    variables = {
      "BUCKET_NAME" = aws_s3_bucket.artifacts_bucket.id
    }
  }

  tags = merge(var.tags, {
    Name        = "${var.project_name}-nuclei-function"
    Component   = "nuclei-lambda"
    Environment = var.environment
  })
}

# Lambda alias for versioning
resource "aws_lambda_alias" "nuclei_alias" {
  name             = var.project_name
  description      = "Nuclei scanner lambda function"
  function_name    = aws_lambda_function.nuclei_function.arn
  function_version = "$LATEST"
}

# Layer to run nuclei in lambda
resource "aws_lambda_layer_version" "nuclei_layer" {
  depends_on          = [aws_s3_object.upload_nuclei]
  layer_name          = "${var.project_name}-nuclei-layer"
  s3_bucket           = aws_s3_bucket.artifacts_bucket.id
  s3_key              = "nuclei.zip"
  compatible_runtimes = ["provided.al2"]
}

# Layer to have nuclei templates
resource "aws_lambda_layer_version" "templates_layer" {
  depends_on          = [aws_s3_object.upload_templates]
  layer_name          = "${var.project_name}-custom-nuclei-templates-layer"
  s3_bucket           = aws_s3_bucket.artifacts_bucket.id
  s3_key              = "custom-nuclei-templates.zip"
  compatible_runtimes = ["provided.al2"]
}

# Layer for nuclei configs
resource "aws_lambda_layer_version" "configs_layer" {
  depends_on          = [aws_s3_object.upload_config]
  layer_name          = "${var.project_name}-nuclei-config-layer"
  s3_bucket           = aws_s3_bucket.artifacts_bucket.id
  s3_key              = "nuclei-configs.zip"
  compatible_runtimes = ["provided.al2"]
}

# ==============================================================================
# GLUE CATALOG FOR FINDINGS
# ==============================================================================

# Glue database for storing scan findings
resource "aws_glue_catalog_database" "nuclei_database" {
  name        = "${var.project_name}_nuclei_db"
  description = "Database for nuclei findings from ${var.project_name}"
}

# Glue table for findings with proper schema
resource "aws_glue_catalog_table" "findings_table" {
  name          = "findings_table"
  database_name = aws_glue_catalog_database.nuclei_database.name
  table_type    = "EXTERNAL_TABLE"

  storage_descriptor {
    location = "s3://${aws_s3_bucket.artifacts_bucket.id}/findings/"

    input_format  = "org.apache.hadoop.mapred.TextInputFormat"
    output_format = "org.apache.hadoop.hive.ql.io.HiveIgnoreKeyTextOutputFormat"

    columns {
      name = "extracted-results"
      type = "array<string>"
    }
    columns {
      name = "host"
      type = "string"
    }
    columns {
      name = "info"
      type = "struct<author:array<string>,classification:string,description:string,name:string,reference:array<string>,severity:string,tags:array<string>>"
    }
    columns {
      name = "metadata"
      type = "struct<shodan-query:string,verified:string>"
    }
    columns {
      name = "matched-at"
      type = "string"
    }
    columns {
      name = "matched-line"
      type = "string"
    }
    columns {
      name = "matcher-status"
      type = "string"
    }
    columns {
      name = "template-id"
      type = "string"
    }
    columns {
      name = "timestamp"
      type = "string"
    }
    columns {
      name = "type"
      type = "string"
    }
    columns {
      name = "matcher-name"
      type = "string"
    }
    columns {
      name = "curl-command"
      type = "string"
    }
    columns {
      name = "ip"
      type = "string"
    }
    columns {
      name = "template-url"
      type = "string"
    }
    columns {
      name = "template"
      type = "string"
    }

    ser_de_info {
      serialization_library = "org.openx.data.jsonserde.JsonSerDe"
      parameters = {
        "serialization.format"      = "1"
        "serialization.null.format" = "null"
      }
    }
  }

  partition_keys {
    name = "dt"
    type = "string"
  }

  parameters = {
    "EXTERNAL"                    = "TRUE"
    "compressionType"             = "none"
    "classification"              = "json"
    "typeOfData"                  = "file"
    "projection.enabled"          = "true"
    "projection.dt.type"          = "date"
    "projection.dt.format"        = "yyyy/MM/dd/HH"
    "projection.dt.interval"      = "1"
    "projection.dt.interval.unit" = "HOURS"
    "projection.dt.range"         = "NOW-3MONTHS,NOW"
    "storage.location.template"   = "s3://${aws_s3_bucket.artifacts_bucket.id}/findings/$${dt}"
  }
}
