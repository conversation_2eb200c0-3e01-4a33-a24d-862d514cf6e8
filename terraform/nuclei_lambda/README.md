# Nuclei Lambda Module

This Terraform module deploys a complete Nuclei scanner infrastructure on AWS Lambda with all required components including S3 storage, Lambda layers, IAM roles, and monitoring capabilities.

## Architecture

The module creates a serverless Nuclei scanning infrastructure:

- **AWS Lambda Function**: Serverless execution environment for Nuclei scans
- **Lambda Layers**: Separate layers for Nuclei binary, custom templates, and configurations
- **S3 Bucket**: Storage for artifacts, templates, and scan findings
- **IAM Roles**: Least-privilege access for Lambda execution
- **CloudWatch**: Comprehensive logging and optional monitoring/alerting
- **AWS Glue**: Data catalog for scan findings analysis

## Quick Start

### 1. Basic Usage

```hcl
module "nuclei_lambda" {
  source = "./nuclei_lambda"

  # Required variables
  project_name = "fast-scan"
  environment  = "dev"

  # Nuclei configuration
  nuclei_version = "3.1.7"
  nuclei_timeout = 900
  memory_size    = 512

  # Optional: Custom paths
  custom_templates_path = "../../nuclear_pond/templates/"
  nuclei_config_path    = "../config"

  tags = {
    Project     = "fast-scan"
    Environment = "dev"
    Component   = "nuclei-scanner"
  }
}
```

### 3. Deploy

```bash
# Initialize Terraform
terraform init

# Plan deployment
terraform plan -target=module.nuclei_lambda

# Deploy the module
terraform apply -target=module.nuclei_lambda
```

## Module Structure

```
nuclei_lambda/
├── main.tf              # Main Lambda function and Glue resources
├── variables.tf         # Input variables with validation
├── outputs.tf          # Output values for other modules
├── iam.tf              # IAM roles and policies
├── s3.tf               # S3 bucket and lifecycle policies
├── artifacts.tf        # Nuclei binary and template management
├── cloudwatch.tf       # CloudWatch logs, alarms, and dashboards
└── README.md           # This documentation
```

## Input Variables

### Required Variables

| Name | Description | Type |
|------|-------------|------|
| `project_name` | Name of the project for resource naming | `string` |

### Core Configuration

| Name | Description | Type | Default |
|------|-------------|------|---------|
| `environment` | Deployment environment (dev/staging/prod) | `string` | `"dev"` |
| `nuclei_version` | Nuclei scanner version to use | `string` | `"3.1.7"` |
| `nuclei_timeout` | Lambda timeout in seconds (max 900) | `number` | `900` |
| `memory_size` | Lambda memory in MB (128-10240) | `number` | `512` |

### Storage Configuration

| Name | Description | Type | Default |
|------|-------------|------|---------|
| `enable_s3_versioning` | Enable S3 bucket versioning | `bool` | `false` |
| `findings_retention_days` | Days to retain findings (0=never delete) | `number` | `90` |
| `s3_lifecycle_enabled` | Enable S3 lifecycle policies | `bool` | `true` |

### Monitoring Configuration

| Name | Description | Type | Default |
|------|-------------|------|---------|
| `log_retention_days` | CloudWatch log retention days | `number` | `30` |
| `enable_cloudwatch_alarms` | Create CloudWatch alarms | `bool` | `false` |
| `enable_cloudwatch_dashboard` | Create monitoring dashboard | `bool` | `false` |
| `enable_lambda_insights` | Enable Lambda Insights | `bool` | `false` |

For a complete list of variables, see [variables.tf](./variables.tf).

## Outputs

### Key Outputs

| Name | Description |
|------|-------------|
| `lambda_function_name` | Name of the Lambda function |
| `lambda_function_arn` | ARN of the Lambda function |
| `s3_bucket_name` | Name of the artifacts S3 bucket |
| `findings_s3_path` | S3 path for scan findings |
| `cloudwatch_log_group_name` | CloudWatch log group name |

### Integration Outputs

These outputs are designed for use by other modules:

```hcl
# Use in other modules
module "backend" {
  source = "./nuclear_pond_backend"

  lambda_function_name = module.nuclei_lambda.lambda_function_name
  lambda_function_arn  = module.nuclei_lambda.lambda_function_arn
  # ... other variables
}
```

For a complete list of outputs, see [outputs.tf](./outputs.tf).

## Environment Support

The module supports multiple environments with isolated resources:

```hcl
# Development environment
module "nuclei_lambda_dev" {
  source = "./nuclei_lambda"

  project_name = "fast-scan"
  environment  = "dev"

  # Development settings
  memory_size            = 512
  log_retention_days     = 7
  findings_retention_days = 30
}

# Production environment
module "nuclei_lambda_prod" {
  source = "./nuclei_lambda"

  project_name = "fast-scan"
  environment  = "prod"

  # Production settings
  memory_size                = 1024
  log_retention_days         = 90
  findings_retention_days    = 365
  enable_cloudwatch_alarms   = true
  enable_cloudwatch_dashboard = true
  enable_lambda_insights     = true
}
```

## Monitoring and Observability

### CloudWatch Logs

All Lambda execution logs are stored in CloudWatch with configurable retention:

```bash
# View logs
aws logs tail /aws/lambda/fast-scan-nuclei-function --follow
```

### CloudWatch Alarms (Optional)

When enabled, the module creates alarms for:
- Error rate monitoring
- Duration monitoring (80% of timeout threshold)
- Throttle detection

### CloudWatch Dashboard (Optional)

Provides a unified view of:
- Lambda function metrics
- S3 bucket usage
- Recent logs
- Performance trends

### Lambda Insights (Optional)

Enhanced monitoring with:
- Detailed performance metrics
- Memory usage analysis
- Cold start tracking

## Security Features

### IAM Least Privilege

The module implements least-privilege access:
- Lambda execution role with minimal required permissions
- S3 access limited to specific bucket and paths
- CloudWatch logs access scoped to function-specific log groups

### S3 Security

- Server-side encryption (AES256)
- Public access blocked
- Optional bucket policy for additional security
- Lifecycle policies for cost optimization

### Network Security

- Lambda function can optionally run in VPC
- Security groups for network-level access control
- Private subnet deployment supported

## Cost Optimization

### S3 Lifecycle Management

Automatic cost optimization through:
- Transition to Infrequent Access after 30 days
- Transition to Glacier after 90 days
- Automatic deletion after retention period
- Cleanup of incomplete multipart uploads

### Lambda Optimization

- Configurable memory allocation (affects CPU and cost)
- Configurable timeout to prevent runaway executions
- Optional reserved concurrency for cost control

## Troubleshooting

### Common Issues

1. **Lambda timeout errors**
   - Increase `nuclei_timeout` variable
   - Check scan complexity and target responsiveness
   - Monitor CloudWatch metrics for duration trends

2. **Memory errors**
   - Increase `memory_size` variable
   - Monitor memory usage in CloudWatch
   - Consider template optimization

3. **Permission errors**
   - Verify IAM role permissions
   - Check S3 bucket policies
   - Ensure Lambda execution role is properly attached

4. **Artifact download failures**
   - Verify internet connectivity during deployment
   - Check Nuclei version availability
   - Ensure GitHub releases are accessible

### Useful Commands

```bash
# Check Lambda function status
aws lambda get-function --function-name fast-scan-nuclei-function

# View recent logs
aws logs tail /aws/lambda/fast-scan-nuclei-function --follow

# Test Lambda function
aws lambda invoke --function-name fast-scan-nuclei-function \
  --payload '{"target": "example.com"}' response.json

# Check S3 bucket contents
aws s3 ls s3://fast-scan-nuclei-artifacts/ --recursive

# Monitor CloudWatch metrics
aws cloudwatch get-metric-statistics \
  --namespace AWS/Lambda \
  --metric-name Duration \
  --dimensions Name=FunctionName,Value=fast-scan-nuclei-function \
  --start-time 2024-01-01T00:00:00Z \
  --end-time 2024-01-02T00:00:00Z \
  --period 3600 \
  --statistics Average
```

## Advanced Configuration

### Custom Templates

Place custom Nuclei templates in the specified directory:

```
nuclear_pond/templates/
├── custom-template-1.yaml
├── custom-template-2.yaml
└── subdirectory/
    └── more-templates.yaml
```

### Configuration Files

Nuclei configuration files in the config directory:

```
config/
├── nuclei-config.yaml
├── .nuclei-ignore
└── custom-settings.yaml
```

### VPC Configuration

To run Lambda in a VPC:

```hcl
module "nuclei_lambda" {
  source = "./nuclei_lambda"

  # ... other variables

  enable_vpc_execution = true

  # VPC configuration would be handled by the calling module
}
```

## Integration Examples

### With Nuclear Pond Backend

```hcl
module "nuclei_lambda" {
  source = "./nuclei_lambda"

  project_name = "fast-scan"
  environment  = "dev"
}

module "nuclear_pond_backend" {
  source = "./nuclear_pond_backend"

  # Pass Lambda details to backend
  lambda_function_name = module.nuclei_lambda.lambda_function_name
  lambda_function_arn  = module.nuclei_lambda.lambda_function_arn

  # ... other variables
}
```

### With EventBridge Integration

```hcl
# Enable S3 notifications for EventBridge
module "nuclei_lambda" {
  source = "./nuclei_lambda"

  # ... other variables

  enable_s3_notifications = true
}

# Create EventBridge rule for new findings
resource "aws_cloudwatch_event_rule" "new_findings" {
  name = "nuclei-new-findings"

  event_pattern = jsonencode({
    source      = ["aws.s3"]
    detail-type = ["Object Created"]
    detail = {
      bucket = {
        name = [module.nuclei_lambda.s3_bucket_name]
      }
      object = {
        key = [{
          prefix = "findings/"
        }]
      }
    }
  })
}
```

## Contributing

When modifying this module:

1. Update variable descriptions and validation rules
2. Add appropriate tags to all resources
3. Update documentation for any new features
4. Test with multiple environments (dev, staging, prod)
5. Follow Terraform best practices
6. Ensure backward compatibility

## Related Documentation

- [Nuclear Pond Backend Module](../nuclear_pond_backend/README.md)
- [Network Module](../network/README.md)
- [AWS Lambda Documentation](https://docs.aws.amazon.com/lambda/)
- [Nuclei Documentation](https://docs.projectdiscovery.io/tools/nuclei/)
