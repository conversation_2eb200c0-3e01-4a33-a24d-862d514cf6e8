# ==============================================================================
# IAM CONFIGURATION FOR NUCLEI LAMBDA
# ==============================================================================

# ==============================================================================
# LAMBDA EXECUTION ROLE
# ==============================================================================

# IAM role for Lambda function execution
resource "aws_iam_role" "lambda_role" {
  name = "${var.project_name}-nuclei-lambda-role"

  assume_role_policy = data.aws_iam_policy_document.lambda_trust_policy.json

  tags = merge(var.tags, {
    Name        = "${var.project_name}-nuclei-lambda-role"
    Component   = "nuclei-lambda"
    Environment = var.environment
  })
}

# Trust policy for Lambda service
data "aws_iam_policy_document" "lambda_trust_policy" {
  statement {
    effect = "Allow"
    
    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com"]
    }
    
    actions = ["sts:AssumeRole"]
  }
}

# ==============================================================================
# LAMBDA EXECUTION POLICY
# ==============================================================================

# Custom IAM policy for Lambda function
resource "aws_iam_policy" "lambda_policy" {
  name        = "${var.project_name}-nuclei-lambda-policy"
  description = "IAM policy for Nuclei Lambda function with minimal required permissions"

  policy = data.aws_iam_policy_document.lambda_policy_document.json

  tags = merge(var.tags, {
    Name        = "${var.project_name}-nuclei-lambda-policy"
    Component   = "nuclei-lambda"
    Environment = var.environment
  })
}

# Policy document with minimal required permissions
# tfsec:ignore:aws-iam-no-policy-wildcards
data "aws_iam_policy_document" "lambda_policy_document" {
  # CloudWatch Logs permissions
  statement {
    sid    = "AllowCloudWatchLogs"
    effect = "Allow"
    
    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents"
    ]
    
    resources = [
      "arn:aws:logs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:log-group:/aws/lambda/${var.project_name}-nuclei-function*"
    ]
  }

  # S3 permissions for uploading scan findings
  statement {
    sid    = "AllowS3FindingsUpload"
    effect = "Allow"
    
    actions = [
      "s3:PutObject",
      "s3:PutObjectAcl"
    ]
    
    resources = [
      "${aws_s3_bucket.artifacts_bucket.arn}/findings/*"
    ]
  }

  # S3 permissions for reading artifacts (layers, configs)
  statement {
    sid    = "AllowS3ArtifactsRead"
    effect = "Allow"
    
    actions = [
      "s3:GetObject"
    ]
    
    resources = [
      "${aws_s3_bucket.artifacts_bucket.arn}/nuclei.zip",
      "${aws_s3_bucket.artifacts_bucket.arn}/custom-nuclei-templates.zip",
      "${aws_s3_bucket.artifacts_bucket.arn}/nuclei-configs.zip"
    ]
  }

  # S3 permissions for listing bucket contents (if needed)
  statement {
    sid    = "AllowS3BucketList"
    effect = "Allow"
    
    actions = [
      "s3:ListBucket"
    ]
    
    resources = [
      aws_s3_bucket.artifacts_bucket.arn
    ]
    
    condition {
      test     = "StringLike"
      variable = "s3:prefix"
      values   = ["findings/*"]
    }
  }

  # VPC permissions (if Lambda needs to run in VPC)
  statement {
    sid    = "AllowVPCAccess"
    effect = "Allow"
    
    actions = [
      "ec2:CreateNetworkInterface",
      "ec2:DescribeNetworkInterfaces",
      "ec2:DeleteNetworkInterface",
      "ec2:AttachNetworkInterface",
      "ec2:DetachNetworkInterface"
    ]
    
    resources = ["*"]
    
    # Only apply if VPC configuration is provided
    condition {
      test     = "StringEquals"
      variable = "aws:RequestedRegion"
      values   = [data.aws_region.current.name]
    }
  }
}

# Attach the custom policy to the Lambda role
resource "aws_iam_role_policy_attachment" "lambda_policy_attachment" {
  role       = aws_iam_role.lambda_role.name
  policy_arn = aws_iam_policy.lambda_policy.arn
}

# ==============================================================================
# AWS MANAGED POLICIES (OPTIONAL)
# ==============================================================================

# Attach AWS managed policy for basic Lambda execution (optional, for additional logging)
resource "aws_iam_role_policy_attachment" "lambda_basic_execution" {
  count      = var.enable_lambda_insights ? 1 : 0
  role       = aws_iam_role.lambda_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

# Attach AWS managed policy for Lambda Insights (if enabled)
resource "aws_iam_role_policy_attachment" "lambda_insights" {
  count      = var.enable_lambda_insights ? 1 : 0
  role       = aws_iam_role.lambda_role.name
  policy_arn = "arn:aws:iam::aws:policy/CloudWatchLambdaInsightsExecutionRolePolicy"
}

# ==============================================================================
# VPC EXECUTION ROLE (IF NEEDED)
# ==============================================================================

# Additional policy for VPC execution (if Lambda runs in VPC)
resource "aws_iam_role_policy_attachment" "lambda_vpc_execution" {
  count      = var.enable_vpc_execution ? 1 : 0
  role       = aws_iam_role.lambda_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole"
}
