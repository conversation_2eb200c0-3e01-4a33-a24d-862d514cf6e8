# ==============================================================================
# NUCLEI LAMBDA MODULE OUTPUTS
# ==============================================================================

# ==============================================================================
# LAMBDA FUNCTION OUTPUTS
# ==============================================================================

output "lambda_function_name" {
  description = "Name of the Nuclei Lambda function"
  value       = aws_lambda_function.nuclei_function.function_name
}

output "lambda_function_arn" {
  description = "ARN of the Nuclei Lambda function"
  value       = aws_lambda_function.nuclei_function.arn
}

output "lambda_function_qualified_arn" {
  description = "Qualified ARN of the Nuclei Lambda function"
  value       = aws_lambda_function.nuclei_function.qualified_arn
}

output "lambda_function_invoke_arn" {
  description = "Invoke ARN of the Nuclei Lambda function"
  value       = aws_lambda_function.nuclei_function.invoke_arn
}

output "lambda_function_version" {
  description = "Version of the Nuclei Lambda function"
  value       = aws_lambda_function.nuclei_function.version
}

output "lambda_alias_arn" {
  description = "ARN of the Lambda function alias"
  value       = aws_lambda_alias.nuclei_alias.arn
}

output "lambda_alias_name" {
  description = "Name of the Lambda function alias"
  value       = aws_lambda_alias.nuclei_alias.name
}

# ==============================================================================
# LAMBDA LAYER OUTPUTS
# ==============================================================================

output "nuclei_layer_arn" {
  description = "ARN of the Nuclei binary Lambda layer"
  value       = aws_lambda_layer_version.nuclei_layer.arn
}

output "templates_layer_arn" {
  description = "ARN of the Nuclei templates Lambda layer"
  value       = aws_lambda_layer_version.templates_layer.arn
}

output "configs_layer_arn" {
  description = "ARN of the Nuclei configs Lambda layer"
  value       = aws_lambda_layer_version.configs_layer.arn
}

output "nuclei_layer_version" {
  description = "Version of the Nuclei binary Lambda layer"
  value       = aws_lambda_layer_version.nuclei_layer.version
}

output "templates_layer_version" {
  description = "Version of the Nuclei templates Lambda layer"
  value       = aws_lambda_layer_version.templates_layer.version
}

output "configs_layer_version" {
  description = "Version of the Nuclei configs Lambda layer"
  value       = aws_lambda_layer_version.configs_layer.version
}

# ==============================================================================
# S3 BUCKET OUTPUTS
# ==============================================================================

output "s3_bucket_name" {
  description = "Name of the S3 artifacts bucket"
  value       = aws_s3_bucket.artifacts_bucket.id
}

output "s3_bucket_arn" {
  description = "ARN of the S3 artifacts bucket"
  value       = aws_s3_bucket.artifacts_bucket.arn
}

output "s3_bucket_domain_name" {
  description = "Domain name of the S3 artifacts bucket"
  value       = aws_s3_bucket.artifacts_bucket.bucket_domain_name
}

output "s3_bucket_regional_domain_name" {
  description = "Regional domain name of the S3 artifacts bucket"
  value       = aws_s3_bucket.artifacts_bucket.bucket_regional_domain_name
}

output "findings_s3_path" {
  description = "S3 path where scan findings are stored"
  value       = "s3://${aws_s3_bucket.artifacts_bucket.id}/findings/"
}

# ==============================================================================
# IAM OUTPUTS
# ==============================================================================

output "lambda_role_arn" {
  description = "ARN of the Lambda execution role"
  value       = aws_iam_role.lambda_role.arn
}

output "lambda_role_name" {
  description = "Name of the Lambda execution role"
  value       = aws_iam_role.lambda_role.name
}

output "lambda_policy_arn" {
  description = "ARN of the Lambda execution policy"
  value       = aws_iam_policy.lambda_policy.arn
}

# ==============================================================================
# CLOUDWATCH OUTPUTS
# ==============================================================================

output "cloudwatch_log_group_name" {
  description = "Name of the CloudWatch log group for the Lambda function"
  value       = aws_cloudwatch_log_group.lambda_log_group.name
}

output "cloudwatch_log_group_arn" {
  description = "ARN of the CloudWatch log group for the Lambda function"
  value       = aws_cloudwatch_log_group.lambda_log_group.arn
}

# ==============================================================================
# GLUE CATALOG OUTPUTS
# ==============================================================================

output "glue_database_name" {
  description = "Name of the Glue catalog database for findings"
  value       = aws_glue_catalog_database.nuclei_database.name
}

output "glue_database_arn" {
  description = "ARN of the Glue catalog database for findings"
  value       = aws_glue_catalog_database.nuclei_database.arn
}

output "glue_table_name" {
  description = "Name of the Glue catalog table for findings"
  value       = aws_glue_catalog_table.findings_table.name
}

output "glue_table_arn" {
  description = "ARN of the Glue catalog table for findings"
  value       = aws_glue_catalog_table.findings_table.arn
}

# ==============================================================================
# CONFIGURATION OUTPUTS
# ==============================================================================

output "nuclei_version" {
  description = "Version of Nuclei scanner being used"
  value       = var.nuclei_version
}

output "nuclei_architecture" {
  description = "Architecture of Nuclei binary being used"
  value       = var.nuclei_arch
}

output "lambda_timeout" {
  description = "Timeout configuration for the Lambda function"
  value       = var.nuclei_timeout
}

output "lambda_memory_size" {
  description = "Memory size configuration for the Lambda function"
  value       = var.memory_size
}

# ==============================================================================
# DEPLOYMENT INFORMATION
# ==============================================================================

output "deployment_info" {
  description = "Comprehensive deployment information for the Nuclei Lambda module"
  value = {
    # Function details
    function_name    = aws_lambda_function.nuclei_function.function_name
    function_arn     = aws_lambda_function.nuclei_function.arn
    alias_name       = aws_lambda_alias.nuclei_alias.name
    
    # Configuration
    nuclei_version   = var.nuclei_version
    timeout_seconds  = var.nuclei_timeout
    memory_mb        = var.memory_size
    
    # Storage
    s3_bucket        = aws_s3_bucket.artifacts_bucket.id
    findings_path    = "s3://${aws_s3_bucket.artifacts_bucket.id}/findings/"
    
    # Monitoring
    log_group        = aws_cloudwatch_log_group.lambda_log_group.name
    
    # Data catalog
    glue_database    = aws_glue_catalog_database.nuclei_database.name
    glue_table       = aws_glue_catalog_table.findings_table.name
    
    # Environment
    environment      = var.environment
    project_name     = var.project_name
  }
}
