# Nuclear Pond Backend Module

This Terraform module deploys the Nuclear Pond backend service on AWS ECS Fargate with Application Load Balancer, ECR repository, and all required infrastructure components.

## Architecture

The module creates a complete, production-ready infrastructure for the Nuclear Pond backend service:

- **ECS Fargate**: Serverless container hosting for the Nuclear Pond application
- **Application Load Balancer**: Internet-facing load balancer for high availability
- **ECR Repository**: Private container registry for Docker images
- **CloudWatch**: Comprehensive logging and monitoring
- **IAM Roles**: Least-privilege access for ECS tasks
- **Security Groups**: Network security with minimal required access

## Quick Start

This module deploys the Nuclear Pond backend. For detailed deployment steps, see the [Deployment Guide](./DEPLOYMENT.md).

### 1. Prerequisites

- Ensure the Network module (or equivalent networking infrastructure) is deployed.
- Ensure the core Lambda function and DynamoDB table (from the root Terraform configuration) are deployed.

### 2. Configure Variables

In your Terraform configuration (e.g., `main.tf` or a dedicated `.tf` file for the backend module):

```hcl
module "nuclear_pond_backend" {
  source = "./nuclear_pond_backend" # Or path to the module

  # Required variables (ensure these are correctly sourced, e.g., from root module outputs or variables)
  project_name            = var.project_name # Or your specific project name
  environment             = "dev" # Or "staging", "prod"
  vpc_id                  = module.network.vpc_id # Or your existing VPC ID
  public_subnet_ids       = module.network.public_subnet_ids # Or your existing public subnet IDs
  private_subnet_ids      = module.network.private_subnet_ids # Or your existing private subnet IDs
  api_key                 = var.nuclearpond_api_key # Sensitive, manage appropriately
  lambda_function_name    = aws_lambda_function.function.function_name # From root module
  lambda_function_arn     = aws_lambda_function.function.arn # From root module
  dynamodb_table_name     = aws_dynamodb_table.scan_state_table.name # From root module
  dynamodb_table_arn      = aws_dynamodb_table.scan_state_table.arn # From root module

  # Optional: Customize as needed
  task_cpu       = "256" # For dev, adjust for staging/prod
  task_memory    = "512" # For dev, adjust for staging/prod
  desired_count  = 1     # For dev, adjust for staging/prod

  tags = {
    Environment = "dev" # Or "staging", "prod"
    Project     = var.project_name # Or your specific project name
    // Add other relevant tags
  }
}
```

### 3. Deploy Infrastructure

```bash
# Initialize Terraform (if not already done)
# terraform init

# Deploy the backend module (ensure dependencies like network are deployed)
terraform apply -target=module.nuclear_pond_backend

# Alternatively, deploy all changes
# terraform apply
```

### 4. Build and Deploy Application

Refer to the [Deployment Guide](./DEPLOYMENT.md#step-3-application-deployment) for instructions on building your Docker image and deploying it to the newly created ECS service. The module generates a helper script (e.g., `deploy-backend-dev.sh`) to simplify this process.

## Module Structure

```
nuclear_pond_backend/
├── main.tf              # Main configuration and deployment automation
├── variables.tf         # Input variables
├── outputs.tf          # Output values
├── ecs.tf              # ECS cluster, service, and task definition
├── ecr.tf              # ECR repository and policies
├── alb.tf              # Application Load Balancer
├── iam.tf              # IAM roles and policies
├── security_groups.tf  # Security groups
├── cloudwatch.tf       # CloudWatch logs and monitoring
├── README.md           # This file
└── DEPLOYMENT.md       # Detailed deployment guide
```

## Input Variables

### Required Variables

| Name | Description | Type |
|------|-------------|------|
| `project_name` | Name of the project | `string` |
| `vpc_id` | ID of the VPC | `string` |
| `public_subnet_ids` | List of public subnet IDs for ALB | `list(string)` |
| `private_subnet_ids` | List of private subnet IDs for ECS | `list(string)` |
| `api_key` | API key for Nuclear Pond service | `string` |
| `lambda_function_name` | Name of the Lambda function | `string` |
| `lambda_function_arn` | ARN of the Lambda function | `string` |
| `dynamodb_table_name` | Name of the DynamoDB table | `string` |
| `dynamodb_table_arn` | ARN of the DynamoDB table | `string` |

### Optional Variables

| Name | Description | Type | Default |
|------|-------------|------|---------|
| `environment` | Deployment environment | `string` | `"dev"` |
| `task_cpu` | CPU units for ECS task | `string` | `"256"` |
| `task_memory` | Memory for ECS task (MB) | `string` | `"512"` |
| `desired_count` | Desired number of ECS tasks | `number` | `1` |
| `container_port` | Container port | `number` | `8080` |
| `health_check_path` | Health check path | `string` | `"/health-check"` |

For a complete list of variables, see [variables.tf](./variables.tf).

## Outputs

This module provides several outputs, including the service URL, ECR repository URL, and ALB DNS name. These are crucial for accessing and managing your deployed backend.

### Key Outputs

| Name                 | Description                                         |
|----------------------|-----------------------------------------------------|
| `service_url`        | URL of the Nuclear Pond service (ALB + path)        |
| `health_check_url`   | Health check URL for the service                    |
| `ecr_repository_url` | ECR repository URL for Docker images                |
| `alb_dns_name`       | ALB DNS name                                        |
| `ecs_cluster_name`   | ECS cluster name                                    |
| `ecs_service_name`   | ECS service name                                    |

For a complete list of outputs, see [outputs.tf](./outputs.tf).

## Environment Support

The module supports multiple environments with isolated resources:

```hcl
# Development environment
module "nuclear_pond_backend_dev" {
  source = "./nuclear_pond_backend"
  environment = "dev"
  # ... other variables
}

# Production environment
module "nuclear_pond_backend_prod" {
  source = "./nuclear_pond_backend"
  environment = "prod"
  task_cpu = "1024"
  task_memory = "2048"
  desired_count = 3
  # ... other variables
}
```

## Monitoring and Observability

The module includes comprehensive monitoring:

- **CloudWatch Logs**: Application logs with configurable retention
- **CloudWatch Metrics**: Custom metrics for errors and scan requests
- **CloudWatch Alarms**: Alerts for high error rates, CPU, and memory usage
- **ECS Container Insights**: Detailed container-level metrics

## Security Features

- **Least Privilege IAM**: Minimal required permissions for ECS tasks
- **Network Security**: Security groups with minimal required access
- **Private Networking**: ECS tasks run in private subnets
- **ECR Security**: Image scanning and lifecycle policies

## Cost Optimization

- **Fargate Spot**: Optional support for cost-effective compute
- **ECR Lifecycle**: Automatic cleanup of old container images
- **Right-sizing**: Configurable CPU and memory allocation

## Troubleshooting

### Common Issues

1. **Service not starting**: Check CloudWatch logs for container errors
2. **Health check failures**: Verify the health check endpoint is responding
3. **Image pull errors**: Ensure ECR permissions and image exists
4. **Network connectivity**: Check security groups and subnet routing

### Useful Commands

```bash
# Check ECS service status
aws ecs describe-services --cluster <cluster-name> --services <service-name>

# View CloudWatch logs
aws logs tail /ecs/<project>/<environment>/nuclearpond --follow

# Force new deployment
aws ecs update-service --cluster <cluster> --service <service> --force-new-deployment
```

## Contributing

When modifying this module:

1. Update variable descriptions and defaults
2. Add appropriate tags to all resources
3. Update documentation for any new features
4. Test with multiple environments
5. Follow Terraform best practices

## Examples

### Production Configuration

```hcl
module "nuclear_pond_backend_prod" {
  source = "./nuclear_pond_backend"

  project_name            = "fast-scan"
  environment            = "prod"
  vpc_id                 = module.network.vpc_id
  public_subnet_ids      = module.network.public_subnet_ids
  private_subnet_ids     = module.network.private_subnet_ids
  api_key                = var.nuclearpond_api_key
  lambda_function_name   = aws_lambda_function.function.function_name
  lambda_function_arn    = aws_lambda_function.function.arn
  dynamodb_table_name    = aws_dynamodb_table.scan_state_table.name
  dynamodb_table_arn     = aws_dynamodb_table.scan_state_table.arn

  # Production sizing
  task_cpu                = "1024"
  task_memory            = "2048"
  desired_count          = 3

  # Enhanced monitoring
  log_retention_days     = 90

  # Security
  enable_deletion_protection = true

  tags = {
    Environment = "prod"
    Project     = "fast-scan"
    CostCenter  = "security"
  }
}
```

### Development Configuration

```hcl
module "nuclear_pond_backend_dev" {
  source = "./nuclear_pond_backend"

  project_name            = "fast-scan"
  environment            = "dev"
  vpc_id                 = module.network.vpc_id
  public_subnet_ids      = module.network.public_subnet_ids
  private_subnet_ids     = module.network.private_subnet_ids
  api_key                = var.nuclearpond_api_key
  lambda_function_name   = aws_lambda_function.function.function_name
  lambda_function_arn    = aws_lambda_function.function.arn
  dynamodb_table_name    = aws_dynamodb_table.scan_state_table.name
  dynamodb_table_arn     = aws_dynamodb_table.scan_state_table.arn

  # Development sizing (minimal resources)
  task_cpu       = "256"
  task_memory    = "512"
  desired_count  = 1

  # Shorter log retention for cost savings
  log_retention_days = 7

  tags = {
    Environment = "dev"
    Project     = "fast-scan"
  }
}
```

## Related Documentation

- [Deployment Guide](./DEPLOYMENT.md) - Detailed deployment instructions
- [Nuclear Pond Documentation](../../nuclear_pond/docs/) - Application documentation
- [AWS ECS Documentation](https://docs.aws.amazon.com/ecs/) - AWS ECS reference
