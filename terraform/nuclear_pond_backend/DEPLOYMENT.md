# Nuclear Pond Backend Deployment Guide

This guide provides comprehensive instructions for deploying the Nuclear Pond backend service using the modular Terraform configuration.

## Prerequisites

Before deploying the Nuclear Pond backend module, ensure the following are in place:

- **AWS Account and CLI**: Configured AWS account with necessary permissions and the AWS CLI installed and configured.
- **Terraform**: Terraform installed (version specified in `versions.tf`, e.g., `>= 1.0`).
- **Docker**: Docker installed for building the application container image.
- **Network Infrastructure**: A configured VPC with public and private subnets, Internet Gateway, NAT Gateway(s), and appropriate route tables. This can be deployed using the `network` module or an existing setup.
- **Core Infrastructure**: The root Terraform configuration should have deployed:
    - The Nuclei Lambda function (e.g., `aws_lambda_function.function`).
    - The DynamoDB table for scan state (e.g., `aws_dynamodb_table.scan_state_table`).
- **Terraform Variables**: Prepare a `terraform.tfvars` file or ensure variables are passed via CLI or environment variables. Key variables include:
    - `project_name`
    - `nuclearpond_api_key` (sensitive, manage securely)
    - Outputs from the network module (VPC ID, subnet IDs) if used.
    - Outputs from the root module (Lambda function name/ARN, DynamoDB table name/ARN).

## Detailed Deployment Steps

These steps assume you are in the `terraform` directory of your project.

### Step 1: Initialize Terraform

If you haven't already, initialize your Terraform configuration:

```bash
terraform init
```

### Step 2: Backend Module Infrastructure Deployment

This step provisions the AWS resources required for the Nuclear Pond backend service, such as ECR, ECS cluster, service, ALB, IAM roles, and security groups.

#### 2.1 Plan the Deployment (Recommended)

Review the changes Terraform will make:

```bash
# Ensure your terraform.tfvars is configured or pass variables via -var or -var-file
terraform plan -target=module.nuclear_pond_backend
```

#### 2.2 Apply the Configuration

Deploy the backend module infrastructure:

```bash
terraform apply -target=module.nuclear_pond_backend
# Confirm by typing "yes" when prompted.
```

This focuses the deployment on the backend module. If you are deploying everything for the first time or have other changes, you might run `terraform apply` without the `-target` flag after ensuring all dependencies (like the network module) are also configured to be deployed.

#### 2.3 Verify Infrastructure Creation

After the apply completes, you can verify the creation of key resources using the AWS CLI or AWS Management Console. Example checks:

```bash
# Check ECR repository (replace <project_name> and <environment> as per your config)
aws ecr describe-repositories --repository-names <project_name>/<environment>/nuclearpond

# Check ECS cluster
aws ecs describe-clusters --clusters <project_name>-<environment>-cluster

# Check ALB
aws elbv2 describe-load-balancers --names <project_name>-<environment>-alb
```

### Step 3: Application Deployment

Once the infrastructure is ready, deploy the Nuclear Pond application (your Go backend) to ECS. This involves building your Docker image, pushing it to the ECR repository created by Terraform, and then updating the ECS service to use the new image.

The module typically generates environment-specific helper scripts (e.g., `deploy-backend-dev.sh`, `deploy-backend-staging.sh`) in the `terraform/nuclear_pond_backend/` directory to automate these application deployment steps. It's highly recommended to use these scripts.

**Example using the generated script (assuming `dev` environment):**

```bash
cd terraform/nuclear_pond_backend # Navigate to where the script is located
./deploy-backend-dev.sh latest # Or specify a particular image tag
```

**Manual Application Deployment Steps (if not using the script):**

1.  **Navigate to your application code:**
    ```bash
    cd ../../nuclear_pond # Or the path to your Go application source code
    ```

2.  **Build the Docker image:**
    ```bash
    docker build -t nuclear-pond:latest . # Tag appropriately
    ```

3.  **Get ECR Repository URL from Terraform Output:**
    ```bash
    # Ensure you are in the terraform directory where you ran `terraform apply`
    cd ../terraform # Or your terraform root
    ECR_URL=$(terraform output -raw nuclear_pond_backend_ecr_repository_url)
    # Note: The output name might vary based on your module alias (e.g., nuclear_pond_backend_dev_ecr_repository_url)
    ```

4.  **Log in to ECR:**
    ```bash
    aws ecr get-login-password --region $(aws configure get region) | docker login --username AWS --password-stdin $ECR_URL
    ```

5.  **Tag and Push Image to ECR:**
    ```bash
    docker tag nuclear-pond:latest $ECR_URL:latest
    docker push $ECR_URL:latest
    # Replace ':latest' with a specific version tag for production deployments
    ```

6.  **Update ECS Service to Use New Image:**
    This forces the ECS service to pull the new image and deploy new tasks.
    ```bash
    # Replace cluster and service names with your actual deployed names (check terraform outputs or ECS console)
    # Example names, adjust according to your project_name and environment:
    CLUSTER_NAME="<project_name>-<environment>-cluster"
    SERVICE_NAME="<project_name>-<environment>-nuclearpond-service"

    aws ecs update-service \
      --cluster $CLUSTER_NAME \
      --service $SERVICE_NAME \
      --force-new-deployment
    ```

### Step 4: Verification

After deployment, verify that the service is running correctly.

#### 4.1 Check Service Health

```bash
# Get ALB DNS name from Terraform output
ALB_DNS=$(terraform output -raw nuclear_pond_backend_alb_dns_name)
# Note: The output name might vary based on your module alias.

# Test health check (path defined in module variables, default /health-check)
curl http://$ALB_DNS/health-check

# Test a basic API endpoint (replace with an actual endpoint of your service)
# Ensure to include the API key if required by your service
# curl -H "Authorization: Bearer <your-api-key>" http://$ALB_DNS/
```

#### 4.2 Monitor Logs and Service Status

```bash
# View ECS service logs (replace log group name as needed)
# Example log group name: /ecs/<project_name>/<environment>/nuclearpond
LOG_GROUP_NAME="/ecs/<project_name>/<environment>/nuclearpond"
aws logs tail $LOG_GROUP_NAME --follow

# Check ECS service status
aws ecs describe-services \
  --cluster $CLUSTER_NAME \
  --services $SERVICE_NAME
```

## Environment Management

This module is designed to be instantiated multiple times for different environments (e.g., dev, staging, prod). This is achieved by calling the module with different `environment` variable values and other environment-specific configurations (like task CPU/memory, desired count, tags) in your Terraform setup.

**Example conceptual structure in your Terraform code (e.g., `environments.tf`):**

```hcl
// terraform/environments.tf (Conceptual)

// Development Environment Backend
module "nuclear_pond_backend_dev" {
  source = "./nuclear_pond_backend"

  project_name            = var.project_name
  environment             = "dev"
  # ... other common variables (VPC, subnets, API key, Lambda/DynamoDB details)
  # ... dev-specific overrides (task_cpu, task_memory, desired_count, log_retention_days)
  tags                    = merge(var.common_tags, { Environment = "dev" })
}

// Staging Environment Backend
module "nuclear_pond_backend_staging" {
  source = "./nuclear_pond_backend"

  project_name            = var.project_name
  environment             = "staging"
  # ... other common variables
  # ... staging-specific overrides
  tags                    = merge(var.common_tags, { Environment = "staging" })
}

// Production Environment Backend
module "nuclear_pond_backend_prod" {
  source = "./nuclear_pond_backend"

  project_name            = var.project_name
  environment             = "prod"
  # ... other common variables
  # ... prod-specific overrides (e.g., higher desired_count, enable_deletion_protection)
  tags                    = merge(var.common_tags, { Environment = "prod" })
}
```

Each instantiation will create a separate set of resources (ECS service, ALB, etc.) for that environment, providing isolation.

## Deployment Automation

### Using the Generated Script

The module generates environment-specific deployment scripts:

```bash
# Development deployment
./deploy-backend-dev.sh latest

# Staging deployment
./deploy-backend-staging.sh v1.2.3

# Production deployment
./deploy-backend-prod.sh v1.2.3
```

### CI/CD Integration

Example GitHub Actions workflow:

```yaml
name: Deploy Nuclear Pond Backend

on:
  push:
    branches: [main]
    paths: ['nuclear_pond/**']

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Deploy to staging
        run: |
          cd terraform/nuclear_pond_backend
          ./deploy-backend-staging.sh ${{ github.sha }}
```

### Blue-Green Deployment

For zero-downtime deployments:

```bash
# Create new task definition with new image
aws ecs register-task-definition --cli-input-json file://task-def.json

# Update service with new task definition
aws ecs update-service \
  --cluster fast-scan-prod-cluster \
  --service fast-scan-prod-nuclearpond-service \
  --task-definition fast-scan-prod-nuclearpond-service:NEW_REVISION
```

## Monitoring and Troubleshooting

### CloudWatch Monitoring

#### Key Metrics to Monitor

- **ECS Service**: CPU utilization, memory utilization, task count
- **ALB**: Request count, response time, error rate
- **Application**: Custom metrics for scan requests and errors

#### CloudWatch Dashboards

Create a dashboard to monitor the service:

```bash
aws cloudwatch put-dashboard \
  --dashboard-name "NuclearPond-${ENVIRONMENT}" \
  --dashboard-body file://dashboard.json
```

#### Log Analysis

```bash
# Search for errors in logs
aws logs filter-log-events \
  --log-group-name "/ecs/fast-scan/dev/nuclearpond" \
  --filter-pattern "ERROR"

# Monitor real-time logs
aws logs tail "/ecs/fast-scan/dev/nuclearpond" --follow
```

### Common Issues and Solutions

#### 1. Service Not Starting

**Symptoms**: ECS tasks keep stopping, service shows 0 running tasks

**Troubleshooting**:
```bash
# Check task definition
aws ecs describe-task-definition --task-definition fast-scan-dev-nuclearpond-service

# Check stopped tasks
aws ecs list-tasks --cluster fast-scan-dev-cluster --service-name fast-scan-dev-nuclearpond-service --desired-status STOPPED

# Get task details
aws ecs describe-tasks --cluster fast-scan-dev-cluster --tasks <task-arn>
```

**Common Causes**:
- Image not found in ECR
- Insufficient IAM permissions
- Invalid environment variables
- Health check failures

#### 2. Health Check Failures

**Symptoms**: ALB shows unhealthy targets

**Troubleshooting**:
```bash
# Check target group health
aws elbv2 describe-target-health --target-group-arn <target-group-arn>

# Test health check endpoint directly
curl http://<alb-dns>/health-check
```

**Solutions**:
- Verify health check path is correct
- Check container port configuration
- Ensure application starts properly

#### 3. Image Pull Errors

**Symptoms**: Tasks fail with "CannotPullContainerError"

**Solutions**:
```bash
# Verify ECR repository exists
aws ecr describe-repositories --repository-names fast-scan/nuclearpond

# Check image exists
aws ecr list-images --repository-name fast-scan/nuclearpond

# Verify IAM permissions for ECR
aws iam get-role-policy --role-name fast-scan-dev-ecs-task-execution-role --policy-name ECRPolicy
```

## Maintenance Operations

### Scaling Operations

#### Manual Scaling

```bash
# Scale up
aws ecs update-service \
  --cluster fast-scan-dev-cluster \
  --service fast-scan-dev-nuclearpond-service \
  --desired-count 3

# Scale down
aws ecs update-service \
  --cluster fast-scan-dev-cluster \
  --service fast-scan-dev-nuclearpond-service \
  --desired-count 1
```