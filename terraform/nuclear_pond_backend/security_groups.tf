# ==============================================================================
# SECURITY GROUPS FOR NUCLEAR POND BACKEND
# ==============================================================================

# Security Group for Application Load Balancer
resource "aws_security_group" "alb_sg" {
  name_prefix = "${var.project_name}-${var.environment}-alb-"
  description = "Security group for Nuclear Pond ALB"
  vpc_id      = var.vpc_id

  # Allow HTTP inbound traffic from internet
  ingress {
    description = "HTTP from Internet"
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Allow HTTPS inbound traffic from internet (for future SSL/TLS support)
  ingress {
    description = "HTTPS from Internet"
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Allow all outbound traffic
  egress {
    description = "All outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(var.tags, {
    Name        = "${var.project_name}-${var.environment}-alb-sg"
    Environment = var.environment
    Component   = "backend"
    Type        = "alb"
  })

  lifecycle {
    create_before_destroy = true
  }
}

# Security Group for ECS Fargate Service
resource "aws_security_group" "ecs_service_sg" {
  name_prefix = "${var.project_name}-${var.environment}-ecs-"
  description = "Security group for Nuclear Pond ECS service"
  vpc_id      = var.vpc_id

  # Allow inbound traffic from ALB on the container port
  ingress {
    description     = "HTTP from ALB"
    from_port       = var.container_port
    to_port         = var.container_port
    protocol        = "tcp"
    security_groups = [aws_security_group.alb_sg.id]
  }

  # Allow all outbound traffic (needed for ECR, Lambda, DynamoDB, etc.)
  egress {
    description = "All outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(var.tags, {
    Name        = "${var.project_name}-${var.environment}-ecs-sg"
    Environment = var.environment
    Component   = "backend"
    Type        = "ecs"
  })

  lifecycle {
    create_before_destroy = true
  }
}

# Optional: Security Group for VPC Endpoints (if using private networking)
resource "aws_security_group" "vpc_endpoints_sg" {
  name_prefix = "${var.project_name}-${var.environment}-vpc-endpoints-"
  description = "Security group for VPC endpoints"
  vpc_id      = var.vpc_id

  # Allow HTTPS traffic from ECS tasks
  ingress {
    description     = "HTTPS from ECS"
    from_port       = 443
    to_port         = 443
    protocol        = "tcp"
    security_groups = [aws_security_group.ecs_service_sg.id]
  }

  # Allow all outbound traffic
  egress {
    description = "All outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(var.tags, {
    Name        = "${var.project_name}-${var.environment}-vpc-endpoints-sg"
    Environment = var.environment
    Component   = "backend"
    Type        = "vpc-endpoints"
  })

  lifecycle {
    create_before_destroy = true
  }
}
