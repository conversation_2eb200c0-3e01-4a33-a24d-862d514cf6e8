{"name": "terraform", "packageManager": "yarn@4.9.1", "scripts": {"clean:images": "aws ecr batch-delete-image --repository-name nuclear-pond-test/nuclearpond --region eu-central-1 --image-ids \"$(aws ecr list-images --repository-name nuclear-pond-test/nuclearpond --region eu-central-1 --query 'imageIds[*]' --output json)\"", "destroy": "yarn clean:images && terraform destroy", "plan": "terraform plan", "apply": "terraform apply"}}