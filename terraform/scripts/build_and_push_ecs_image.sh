#!/bin/bash

set -e # Exit immediately if a command exits with a non-zero status.

# <PERSON><PERSON><PERSON> to build, tag, and push the Nuclear Pond Docker image to ECR.

# --- Configuration & Arguments ---
ECR_REPOSITORY_URL="${1}"
IMAGE_TAG="${2:-latest}" # Default to 'latest' if no tag is provided

# Relative path to the Dockerfile and build context from this script's location
DOCKER_BUILD_CONTEXT_PATH="../nuclear_pond/"
DOCKERFILE_PATH="../nuclear_pond/Dockerfile"

# --- Sanity Checks ---
if [ -z "${ECR_REPOSITORY_URL}" ]; then
  echo "Error: ECR Repository URL (first argument) is required." >&2
  echo "Usage: $0 <ecr-repository-url> [image-tag]" >&2
  exit 1
fi

if ! command -v aws &> /dev/null; then
    echo "Error: AWS CLI is not installed or not in PATH." >&2
    exit 1
fi

if ! command -v docker &> /dev/null; then
    echo "Error: Docker is not installed or not in PATH." >&2
    exit 1
fi

if [ ! -f "${DOCKERFILE_PATH}" ]; then
    echo "Error: Dockerfile not found at ${DOCKERFILE_PATH}" >&2
    echo "Please ensure the script is run from a location where this relative path is correct, or adjust DOCKERFILE_PATH." >&2
    exit 1
fi

# --- Infer AWS Region and Account ID ---
AWS_REGION=$(aws configure get region || echo "")
if [ -z "${AWS_REGION}" ]; then
    echo "Error: Could not automatically determine AWS region. Please configure AWS CLI or set AWS_REGION environment variable." >&2
    exit 1
fi

AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text || echo "")
if [ -z "${AWS_ACCOUNT_ID}" ]; then
    echo "Error: Could not automatically determine AWS Account ID. Please ensure AWS CLI is configured and authenticated." >&2
    exit 1
fi

ECR_REGISTRY="${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com"
IMAGE_NAME_WITH_TAG="${ECR_REPOSITORY_URL}:${IMAGE_TAG}"

# --- Main Script Logic ---

echo "--- Logging in to ECR (${ECR_REGISTRY}) ---"
aws ecr get-login-password --region "${AWS_REGION}" | docker login --username AWS --password-stdin "${ECR_REGISTRY}"
if [ $? -ne 0 ]; then
  echo "Error: Docker login to ECR failed." >&2
  exit 1
fi
echo "ECR login successful."

echo "
--- Building Docker image (${IMAGE_NAME_WITH_TAG}) ---"
echo "Build context: ${DOCKER_BUILD_CONTEXT_PATH}"
echo "Dockerfile: ${DOCKERFILE_PATH}"
docker build -t "${IMAGE_NAME_WITH_TAG}" -f "${DOCKERFILE_PATH}" "${DOCKER_BUILD_CONTEXT_PATH}"
if [ $? -ne 0 ]; then
  echo "Error: Docker build failed." >&2
  exit 1
fi
echo "Docker build successful."

# The docker build command above already tags the image with the full ECR_REPOSITORY_URL:IMAGE_TAG
# So, an explicit 'docker tag' might be redundant if ECR_REPOSITORY_URL is already the image name part.
# However, if ECR_REPOSITORY_URL was just the repo name (e.g., my-app) and not the full URL path,
# then a distinct tag would be needed. The current setup assumes ECR_REPOSITORY_URL is the full path.

echo "
--- Pushing image to ECR (${IMAGE_NAME_WITH_TAG}) ---"
docker push "${IMAGE_NAME_WITH_TAG}"
if [ $? -ne 0 ]; then
  echo "Error: Docker push to ECR failed." >&2
  exit 1
fi
echo "Docker push successful! Image URI: ${IMAGE_NAME_WITH_TAG}"

echo "
--- Script finished successfully ---" 