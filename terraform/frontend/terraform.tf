# ==============================================================================
# TERRAFORM CONFIGURATION FOR FRONTEND MODULE
# ==============================================================================

terraform {
  # Specify required Terraform version
  required_version = ">= 1.0"

  # Specify required providers and their versions
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = ">= 4.50.0"
      
      # Define provider configurations that this module expects
      configuration_aliases = [
        aws.us_east_1  # Required for ACM certificates used with CloudFront
      ]
    }
    
    local = {
      source  = "hashicorp/local"
      version = ">= 2.0"
    }
  }
}

# ==============================================================================
# PROVIDER CONFIGURATION NOTES
# ==============================================================================
#
# This module requires two AWS provider configurations:
#
# 1. Default AWS provider (aws) - for most resources
#    - Uses the region where the module is deployed
#    - Configured automatically by Terraform
#
# 2. US East 1 provider (aws.us_east_1) - for CloudFront ACM certificates
#    - Must be explicitly configured in the calling module
#    - Required because CloudFront only accepts certificates from us-east-1
#
# Example usage in calling module:
#
# provider "aws" {
#   alias  = "us_east_1"
#   region = "us-east-1"
# }
#
# module "frontend" {
#   source = "./frontend"
#   
#   providers = {
#     aws.us_east_1 = aws.us_east_1
#   }
#   
#   # ... other variables
# }
#
# ==============================================================================
