# ==============================================================================
# S3 BUCKET OUTPUTS
# ==============================================================================

output "frontend_s3_bucket_name" {
  description = "Name of the S3 bucket hosting the frontend static files"
  value       = aws_s3_bucket.frontend.id
}

output "frontend_s3_bucket_arn" {
  description = "ARN of the S3 bucket (useful for IAM policies and cross-account access)"
  value       = aws_s3_bucket.frontend.arn
}

output "frontend_s3_website_endpoint" {
  description = "S3 static website endpoint (used when CloudFront is disabled)"
  value       = aws_s3_bucket_website_configuration.frontend.website_endpoint
}

output "frontend_s3_website_url" {
  description = "Full HTTP URL for S3 static website (used when CloudFront is disabled)"
  value       = "http://${aws_s3_bucket_website_configuration.frontend.website_endpoint}"
}

# ==============================================================================
# CLOUDFRONT OUTPUTS (only when CloudFront is enabled)
# ==============================================================================

output "cloudfront_distribution_id" {
  description = "CloudFront distribution ID (used for cache invalidation)"
  value       = var.enable_cloudfront ? aws_cloudfront_distribution.frontend[0].id : null
}

output "cloudfront_distribution_domain_name" {
  description = "CloudFront distribution domain name (e.g., d123456789.cloudfront.net)"
  value       = var.enable_cloudfront ? aws_cloudfront_distribution.frontend[0].domain_name : null
}

output "cloudfront_distribution_arn" {
  description = "CloudFront distribution ARN (useful for IAM policies)"
  value       = var.enable_cloudfront ? aws_cloudfront_distribution.frontend[0].arn : null
}

# ==============================================================================
# ACCESS URLS
# ==============================================================================

output "frontend_url" {
  description = "Primary URL to access the frontend application"
  value = var.enable_cloudfront ? (
    # CloudFront enabled: use custom domain if available, otherwise CloudFront domain
    var.create_route53_record && var.frontend_domain_name != "" ? 
      "https://${var.frontend_domain_name}" : 
      "https://${aws_cloudfront_distribution.frontend[0].domain_name}"
  ) : (
    # CloudFront disabled: use S3 website endpoint
    "http://${aws_s3_bucket_website_configuration.frontend.website_endpoint}"
  )
}

output "all_frontend_urls" {
  description = "All available URLs to access the frontend (for testing and reference)"
  value = {
    # Primary URL (recommended)
    primary = var.enable_cloudfront ? (
      var.create_route53_record && var.frontend_domain_name != "" ? 
        "https://${var.frontend_domain_name}" : 
        "https://${aws_cloudfront_distribution.frontend[0].domain_name}"
    ) : "http://${aws_s3_bucket_website_configuration.frontend.website_endpoint}"
    
    # S3 direct URL (always available)
    s3_direct = "http://${aws_s3_bucket_website_configuration.frontend.website_endpoint}"
    
    # CloudFront URL (when enabled)
    cloudfront = var.enable_cloudfront ? "https://${aws_cloudfront_distribution.frontend[0].domain_name}" : null
    
    # Custom domain URL (when configured)
    custom_domain = var.create_route53_record && var.frontend_domain_name != "" ? "https://${var.frontend_domain_name}" : null
  }
}

# ==============================================================================
# DEPLOYMENT INFORMATION
# ==============================================================================

output "deployment_info" {
  description = "Summary of deployment configuration and next steps"
  value = {
    environment     = var.environment
    cloudfront_enabled = var.enable_cloudfront
    custom_domain   = var.frontend_domain_name != "" ? var.frontend_domain_name : "Not configured"
    s3_bucket      = aws_s3_bucket.frontend.id
    
    next_steps = var.enable_cloudfront ? [
      "1. Build your frontend: cd frontend && yarn build:${var.environment}",
      "2. Deploy to S3: aws s3 sync dist/ s3://${aws_s3_bucket.frontend.id} --delete",
      "3. Invalidate CloudFront cache: aws cloudfront create-invalidation --distribution-id ${aws_cloudfront_distribution.frontend[0].id} --paths '/*'",
      "4. Access your app at: ${var.create_route53_record && var.frontend_domain_name != "" ? "https://${var.frontend_domain_name}" : "https://${aws_cloudfront_distribution.frontend[0].domain_name}"}"
    ] : [
      "1. Build your frontend: cd frontend && yarn build:${var.environment}",
      "2. Deploy to S3: aws s3 sync dist/ s3://${aws_s3_bucket.frontend.id} --delete",
      "3. Access your app at: http://${aws_s3_bucket_website_configuration.frontend.website_endpoint}"
    ]
  }
}
