# Frontend Module ToDo Plan & Review Items

This document outlines tasks and considerations for the frontend Terraform module that require review or action.

## ✅ Recently Completed Tasks

1. **✅ COMPLETED: Refactor CloudFront Origin to Use S3 REST API with OAC**
   - **Status:** Implemented in the recent optimization
   - **Changes Made:** 
     - CloudFront origin now uses `aws_s3_bucket.frontend.bucket_regional_domain_name`
     - Implemented AWS Origin Access Control (OAC) with `aws_cloudfront_origin_access_control.frontend`
     - Updated S3 bucket policy to grant secure access to CloudFront via OAC
     - Maintained backward compatibility for direct S3 access when CloudFront is disabled

## High Priority Tasks

1. **CRITICAL: `.env` File Security Review**
   - **Current State:** The `local_file.frontend_env` resource writes sensitive variables including `VITE_API_KEY` to files (`../../frontend/.env.${var.environment}`) within the frontend project directory.
   - **Action Required:** **URGENTLY VERIFY** that the paths `../../frontend/.env.*` are correctly included in the frontend project's `.gitignore` file.
   - **Risk:** Although the `api_key` variable is correctly marked as `sensitive = true` in Terraform, committing the generated `.env` files to source control would expose sensitive information.
   - **Recommendation:** Add the following to `.gitignore` if not already present:
     ```
     # Environment files with secrets
     .env.dev
     .env.staging
     .env.prod
     .env.local
     ```

2. **Review S3 Bucket CORS Configuration Optimization**
   - **Current State:** S3 bucket has CORS configuration that supports both CloudFront and direct access scenarios.
   - **Consideration:** The current implementation allows both CloudFront and direct S3 access when CloudFront is enabled. Consider if this dual access is needed for production environments.
   - **Action:** Review if the dual access pattern in the `frontend_policy_with_cloudfront` policy is appropriate for security requirements, or if CloudFront-only access should be enforced for production.
   - **Note:** The CORS configuration properly handles different origins based on deployment mode, but review if the localhost entries in development mode are sufficient.

## Future Enhancements & Optimization

3. **Deployment Workflow Integration with CI/CD**
   - **Current State:** Automated deployment scripts are now generated with comprehensive error handling and status reporting.
   - **Enhancement Opportunity:** For production automation, integrate the frontend build, S3 sync, and CloudFront invalidation directly into CI/CD pipelines (e.g., GitHub Actions, GitLab CI, AWS CodePipeline).
   - **Implementation:** Use Terraform outputs (S3 bucket name, CloudFront distribution ID) in CI/CD and leverage secure secret management for AWS credentials.

4. **IAM Access Key Handling Best Practices**
   - **Current State:** No IAM user creation for deployment in the current implementation (deployment uses existing AWS CLI credentials).
   - **Consideration:** If IAM users are added for automated deployment, ensure secret keys are handled securely.
   - **Recommendation:** For CI/CD automation, consider using IAM roles with OIDC (for GitHub Actions) or instance profiles (for AWS-based CI/CD) instead of long-lived access keys.

5. **Root Module Variable Sensitivity**
   - **Current State:** The `api_key` variable passed to this module uses `var.nuclearpond_api_key`.
   - **Action:** Ensure that `var.nuclearpond_api_key` is correctly defined in the root module's `variables.tf` file and is marked with `sensitive = true`.
   - **Verification:** Check that the root module properly handles sensitive variables to prevent accidental exposure in logs or console output.

6. **Performance Monitoring and Optimization**
   - **New Consideration:** With the optimized CloudFront configuration using managed cache policies, consider implementing:
     - CloudWatch alarms for cache hit ratios
     - Performance monitoring for deployment script execution times
     - Cost optimization reviews for different deployment modes

## Documentation and Maintenance

7. **Keep Documentation Updated**
   - **Current State:** Comprehensive documentation has been created covering deployment modes, architecture, and troubleshooting.
   - **Ongoing Task:** Ensure documentation stays current with any infrastructure changes.
   - **Review Frequency:** Quarterly review of documentation for accuracy and completeness.

## Security Considerations

8. **Security Headers Enhancement**
   - **Current State:** CloudFront uses AWS managed cache policies which include basic security headers.
   - **Future Enhancement:** Consider implementing custom response headers policy for additional security headers (CSP, HSTS, etc.) based on application requirements.

9. **Access Logging Implementation**
   - **Consideration:** Implement S3 access logs and CloudFront access logs for security monitoring and troubleshooting.
   - **Implementation:** Optional feature that can be enabled based on compliance and monitoring requirements.