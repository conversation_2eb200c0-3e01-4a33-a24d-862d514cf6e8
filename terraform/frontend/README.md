# Frontend Module

This Terraform module deploys the Nuclear Pond frontend as a static website on AWS S3 with optional CloudFront CDN distribution for global content delivery.

## Architecture

The module creates a complete frontend hosting solution:

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend Architecture                    │
├─────────────────────────┬───────────────────────────────────┤
│     Development Mode    │        Production Mode            │
│                         │                                   │
│  ┌─────────────────┐   │   ┌─────────────────────────────┐ │
│  │ S3 Static       │   │   │ CloudFront Distribution     │ │
│  │ Website         │   │   │ - Global CDN                │ │
│  │ - Direct access │   │   │ - HTTPS/SSL                 │ │
│  │ - HTTP only     │   │   │ - Custom domain support    │ │
│  │ - Fast deploy   │   │   │ - Caching                   │ │
│  └─────────────────┘   │   └─────────────────────────────┘ │
│           │             │                   │               │
│           │             │                   │               │
│  ┌─────────────────┐   │   ┌─────────────────────────────┐ │
│  │ S3 Bucket       │   │   │ S3 Bucket (Origin)          │ │
│  │ - Static files  │   │   │ - Static files              │ │
│  │ - Public read   │   │   │ - Private (OAC access)     │ │
│  └─────────────────┘   │   └─────────────────────────────┘ │
└─────────────────────────┴───────────────────────────────────┘
```

## Features

- ✅ **Dual Deployment Modes**: Development (S3 direct) and Production (CloudFront)
- ✅ **Automated Build & Deploy**: Generated deployment scripts
- ✅ **Environment Configuration**: Automatic .env file generation
- ✅ **Custom Domain Support**: Route53 integration with ACM certificates
- ✅ **Security Best Practices**: Origin Access Control (OAC) for CloudFront
- ✅ **Cost Optimization**: Configurable CloudFront price classes
- ✅ **Modern Terraform**: Compatible with count, for_each, depends_on

## Quick Start

### Basic Usage

```hcl
# Configure the required us-east-1 provider for ACM certificates
provider "aws" {
  alias  = "us_east_1"
  region = "us-east-1"
}

module "frontend" {
  source = "./frontend"

  # Provider configuration
  providers = {
    aws.us_east_1 = aws.us_east_1
  }

  # Required variables
  project_name = "fast-scan"
  environment  = "dev"
  api_url      = "http://your-backend-alb-dns-name"
  api_key      = "your-secure-api-key"

  tags = {
    Environment = "dev"
    Project     = "fast-scan"
  }
}
```

### Production Configuration

```hcl
module "frontend" {
  source = "./frontend"

  providers = {
    aws.us_east_1 = aws.us_east_1
  }

  project_name = "fast-scan"
  environment  = "prod"
  api_url      = "http://your-backend-alb-dns-name"
  api_key      = "your-secure-api-key"

  # Enable CloudFront for production
  enable_cloudfront = true
  cloudfront_price_class = "PriceClass_100"

  # Custom domain configuration
  frontend_domain_name = "app.yourdomain.com"
  create_route53_record = true
  route53_zone_id = "Z1234567890ABC"

  tags = {
    Environment = "prod"
    Project     = "fast-scan"
  }
}
```

### Conditional Deployment

```hcl
module "frontend" {
  source = "./frontend"
  count  = var.enable_frontend_deployment ? 1 : 0

  providers = {
    aws.us_east_1 = aws.us_east_1
  }

  # ... other configuration
}
```

## Input Variables

### Required Variables

| Name | Description | Type |
|------|-------------|------|
| `project_name` | Name of the project | `string` |
| `api_url` | URL of the backend API | `string` |
| `api_key` | API key for backend authentication | `string` |

### Optional Variables

| Name | Description | Type | Default |
|------|-------------|------|---------|
| `environment` | Deployment environment | `string` | `"dev"` |
| `enable_cloudfront` | Whether to create CloudFront distribution | `bool` | `false` |
| `frontend_domain_name` | Custom domain name | `string` | `""` |
| `create_route53_record` | Whether to create Route53 records | `bool` | `false` |
| `demo_password` | Demo password for the application | `string` | `"TestPass"` |

For a complete list of variables, see [variables.tf](./variables.tf).

## Outputs

### Key Outputs

| Name | Description |
|------|-------------|
| `frontend_url` | Primary URL for accessing the frontend |
| `frontend_s3_bucket_name` | Name of the S3 bucket |
| `frontend_cloudfront_domain_name` | CloudFront distribution domain (if enabled) |
| `deployment_info` | Complete deployment information |

For a complete list of outputs, see [outputs.tf](./outputs.tf).

## Deployment Modes

### Development Mode (enable_cloudfront = false)

- **Direct S3 hosting**: Fast deployment, no cache invalidation needed
- **HTTP only**: No SSL/TLS encryption
- **Cost effective**: No CloudFront charges
- **Best for**: Development, testing, rapid iteration

### Production Mode (enable_cloudfront = true)

- **Global CDN**: CloudFront distribution for worldwide performance
- **HTTPS/SSL**: Automatic SSL certificate via ACM
- **Custom domains**: Support for your own domain names
- **Caching**: Improved performance and reduced S3 costs
- **Best for**: Production, staging, public-facing deployments

## Automated Deployment

The module generates environment-specific deployment scripts:

```bash
# Deploy infrastructure
terraform apply -target=module.frontend

# Deploy frontend code
cd terraform/frontend
./deploy-frontend-dev.sh

# Or for production
./deploy-frontend-prod.sh v1.2.3
```

## Provider Requirements

This module requires two AWS provider configurations:

1. **Default provider**: For most resources (S3, Route53, etc.)
2. **us-east-1 provider**: For ACM certificates used with CloudFront

```hcl
# Required in calling module
provider "aws" {
  alias  = "us_east_1"
  region = "us-east-1"
}

module "frontend" {
  source = "./frontend"
  
  providers = {
    aws.us_east_1 = aws.us_east_1
  }
  
  # ... other variables
}
```

## Security Features

- **Origin Access Control (OAC)**: Secure CloudFront to S3 access
- **Private S3 bucket**: When CloudFront is enabled
- **HTTPS enforcement**: Automatic SSL certificates
- **Sensitive variable handling**: API keys marked as sensitive

## Environment Management

Support for multiple environments with different configurations:

```bash
# Development
terraform apply -var="environment=dev" -var="enable_cloudfront=false"

# Staging
terraform apply -var="environment=staging" -var="enable_cloudfront=true"

# Production
terraform apply -var="environment=prod" \
                -var="enable_cloudfront=true" \
                -var="frontend_domain_name=app.yourdomain.com"
```

## Troubleshooting

### Common Issues

1. **Count/for_each errors**: Ensure no local provider configurations in module
2. **ACM certificate issues**: Verify us-east-1 provider is configured
3. **CloudFront deployment slow**: Cache invalidation can take 5-15 minutes
4. **Custom domain issues**: Check Route53 zone configuration

### Useful Commands

```bash
# Check S3 bucket
aws s3 ls s3://your-bucket-name

# Check CloudFront distribution
aws cloudfront list-distributions

# Invalidate CloudFront cache
aws cloudfront create-invalidation --distribution-id ABCD123 --paths "/*"

# Check ACM certificates
aws acm list-certificates --region us-east-1
```

## Migration from Legacy Module

If migrating from a module with local provider configurations:

1. Remove any `provider` blocks from module files
2. Add `terraform.tf` with required_providers configuration
3. Update calling module to pass providers
4. Test with `terraform plan` before applying

## Contributing

When modifying this module:

1. Update variable descriptions and validation
2. Add appropriate tags to all resources
3. Update this documentation
4. Test with both CloudFront enabled and disabled
5. Verify provider configuration works correctly

## Related Documentation

- [Deployment Guide](./DEPLOYMENT-DEV.md) - Generated deployment instructions
- [Main Infrastructure](../README.md) - Complete infrastructure setup
- [Nuclear Pond Backend](../nuclear_pond_backend/README.md) - Backend service
- [AWS CloudFront Documentation](https://docs.aws.amazon.com/cloudfront/) - CloudFront reference
