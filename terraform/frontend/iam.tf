# IAM user for CI/CD deployment to S3 and CloudFront
resource "aws_iam_user" "frontend_deployer" {
  name = "${var.project_name}-frontend-deployer-${var.environment}"
  path = "/system/"

  tags = merge(var.tags, {
    Name        = "${var.project_name}-frontend-deployer-${var.environment}"
    Environment = var.environment
  })
}

# IAM policy for frontend deployment
resource "aws_iam_policy" "frontend_deployer" {
  name        = "${var.project_name}-frontend-deployer-policy-${var.environment}"
  description = "Policy for deploying frontend to S3 and invalidating CloudFront cache"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "s3:ListBucket",
          "s3:GetBucketLocation"
        ]
        Effect   = "Allow"
        Resource = aws_s3_bucket.frontend.arn
      },
      {
        Action = [
          "s3:PutObject",
          "s3:GetObject",
          "s3:DeleteObject",
          "s3:PutObjectAcl"
        ]
        Effect   = "Allow"
        Resource = "${aws_s3_bucket.frontend.arn}/*"
      },
      {
        Action = [
          "cloudfront:CreateInvalidation",
          "cloudfront:GetInvalidation",
          "cloudfront:ListInvalidations"
        ]
        Effect   = "Allow"
        Resource = var.enable_cloudfront ? aws_cloudfront_distribution.frontend[0].arn : "*"
      }
    ]
  })
}

# Attach policy to user
resource "aws_iam_user_policy_attachment" "frontend_deployer" {
  user       = aws_iam_user.frontend_deployer.name
  policy_arn = aws_iam_policy.frontend_deployer.arn
}

# Create access key for the IAM user
resource "aws_iam_access_key" "frontend_deployer" {
  user = aws_iam_user.frontend_deployer.name
}

# Output the access key and secret key (sensitive)
output "frontend_deployer_access_key" {
  description = "Access key for the frontend deployer IAM user"
  value       = aws_iam_access_key.frontend_deployer.id
}

output "frontend_deployer_secret_key" {
  description = "Secret key for the frontend deployer IAM user"
  value       = aws_iam_access_key.frontend_deployer.secret
  sensitive   = true
}
