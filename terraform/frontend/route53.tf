# Route53 record for the frontend
resource "aws_route53_record" "frontend" {
  count = var.create_route53_record && var.frontend_domain_name != "" && var.route53_zone_id != "" ? 1 : 0

  zone_id = var.route53_zone_id
  name    = var.frontend_domain_name
  type    = "A"

  alias {
    name                   = aws_cloudfront_distribution.frontend[0].domain_name
    zone_id                = aws_cloudfront_distribution.frontend[0].hosted_zone_id
    evaluate_target_health = false
  }
}

# DNS validation records for ACM certificate
resource "aws_route53_record" "frontend_validation" {
  for_each = var.create_route53_record && var.frontend_domain_name != "" && var.route53_zone_id != "" && var.enable_cloudfront ? {
    for dvo in aws_acm_certificate.frontend[0].domain_validation_options : dvo.domain_name => {
      name   = dvo.resource_record_name
      record = dvo.resource_record_value
      type   = dvo.resource_record_type
    }
  } : {}

  allow_overwrite = true
  name            = each.value.name
  records         = [each.value.record]
  ttl             = 60
  type            = each.value.type
  zone_id         = var.route53_zone_id
}

# Certificate validation
resource "aws_acm_certificate_validation" "frontend" {
  count = var.create_route53_record && var.frontend_domain_name != "" && var.route53_zone_id != "" && var.enable_cloudfront ? 1 : 0

  provider                = aws.us_east_1
  certificate_arn         = aws_acm_certificate.frontend[0].arn
  validation_record_fqdns = [for record in aws_route53_record.frontend_validation : record.fqdn]
}
