# Installation Guide

This guide will walk you through the process of installing Nuclear Pond and its dependencies.

## Prerequisites

Before installing Nuclear Pond, ensure you have the following prerequisites:

- Go 1.16 or later
- Nuclei (for local execution mode)
- AWS account (for cloud execution mode)

### Installing Go

If you don't have Go installed, you can download it from the [official Go website](https://golang.org/dl/).

### Installing Nuclei

For local execution mode, you need to have Nuclei installed. There are two ways to install Nuclei:

#### Option 1: Using the built-in installer script

<PERSON> Pond provides a built-in script to download and install Nuclei:

```bash
yarn install:nuclei
```

This will download and install the latest version of Nuclei in the `bin` directory of your Nuclear Pond installation.

#### Option 2: Manual installation using Go

You can also install Nuclei manually using Go:

```bash
go install -v github.com/projectdiscovery/nuclei/v3/cmd/nuclei@latest
```

Verify the installation:

```bash
# If installed using the script
./bin/nuclei -version

# If installed using Go
nuclei -version
```

## Installing Nuclear Pond

1. Build the binary:

```bash
go build -o nuclearpond
```

## Setting Up AWS (for Cloud Execution)

If you plan to use the cloud execution mode, you need to set up AWS resources.

1. Set the required environment variables:

```bash
export AWS_REGION=<your-aws-region>
export AWS_LAMBDA_FUNCTION_NAME=<your-lambda-function-name>
```

## Verifying the Installation

To verify that Nuclear Pond is installed correctly, run:

```bash
nuclearpond --help
```

You should see the help output with the available commands and options.

## Next Steps

Now that you have installed Nuclear Pond, you can proceed to the [Usage Guide](usage.md) to learn how to use it.
