# Nuclear Pond Documentation

Welcome to the Nuclear Pond documentation. This guide will help you understand how to use Nuclear Pond to run nuclei scans in parallel, both in the cloud using AWS Lambda and locally on your machine.

## Table of Contents

- [Overview](#overview)
- [Installation](#installation)
- [Usage](#usage)
  - [Cloud Execution (AWS)](#cloud-execution-aws)
  - [Local Execution](#local-execution)
- [Nuclei Integration](#nuclei-integration)
- [Configuration](#configuration)
- [Custom Templates](#custom-templates)
- [Examples](#examples)
- [Contributing](#contributing)

## Overview

Nuclear Pond is a tool designed to run [Nuclei](https://github.com/projectdiscovery/nuclei) vulnerability scans in parallel. It offers two execution modes:

1. **Cloud Execution**: Leverages AWS Lambda to run scans in the cloud, providing scalability and cost efficiency.
2. **Local Execution**: Runs scans directly on your local machine, bypassing the need for AWS infrastructure.

Both modes support parallel execution, custom templates, and various output formats.

## Installation

For detailed installation instructions, see the [Installation Guide](installation.md).

## Usage

Nuclear Pond provides a command-line interface with several subcommands:

- `run`: Execute nuclei scans in the cloud using AWS Lambda
- `local`: Execute nuclei scans locally on your machine
- `service`: Start an API server for executing scans

For detailed usage instructions, see the [Usage Guide](usage.md).

## Nuclei Integration

Nuclear Pond provides seamless integration with Nuclei, allowing you to:

- Automatically install Nuclei using a built-in script
- Run Nuclei directly or through Nuclear Pond
- Use custom templates for scanning
- Execute scans in parallel for improved performance

For detailed information on using Nuclei with Nuclear Pond, see the [Nuclei Integration Guide](nuclei-integration.md).

## Configuration

For information on configuring Nuclear Pond, see the [Configuration Guide](configuration.md).

## Custom Templates

For information on using custom templates with Nuclear Pond, see the [Custom Templates Guide](custom-templates.md).

## Examples

For examples of common use cases, see the [Examples Guide](examples.md).
