# Nuclei Integration Guide

This guide explains how to install, configure, and use Nuclei with Nuclear Pond.

## Overview

Nuclear Pond provides seamless integration with Nuclei, allowing you to:

1. Automatically install Nuclei using a built-in script
2. Run Nuclei directly or through Nuclear Pond
3. Use custom templates for scanning
4. Execute scans in parallel for improved performance

## Installing Nuclei

### Automatic Installation

Nuclear Pond provides a script to automatically download and install Nuclei:

```bash
# Using yarn
yarn install:nuclei
```

This script:
- Detects your operating system and architecture
- Downloads the appropriate Nuclei binary
- Extracts it to the `bin` directory in your Nuclear Pond installation
- Makes the binary executable (on Unix-like systems)

The installed binary will be available at `./bin/nuclei`.

### Manual Installation

If you prefer to install Nuclei manually, you can:

1. Install using Go:
   ```bash
   go install -v github.com/projectdiscovery/nuclei/v3/cmd/nuclei@latest
   ```

2. Download from the official GitHub repository:
   ```bash
   # For Linux
   curl -LO https://github.com/projectdiscovery/nuclei/releases/download/v3.1.7/nuclei_3.1.7_linux_amd64.zip
   unzip nuclei_3.1.7_linux_amd64.zip
   chmod +x nuclei
   ```

## Running Nuclei

### Direct Execution

You can run Nuclei directly using the provided npm/yarn script:

```bash
yarn run:nuclei
```

This command runs Nuclei with the targets specified in `targets.txt` and templates from the `./templates/` directory.

### Using Nuclear Pond with Local Nuclei

Nuclear Pond's local execution mode can use the installed Nuclei binary:

```bash
# Using yarn
yarn run:nuclear_pond_cli

# Using npm
npm run run:nuclear_pond_cli
```

This command runs the Nuclear Pond local execution mode, which:
1. Looks for Nuclei in the `bin` directory first
2. Falls back to the system-installed Nuclei if not found
3. Executes Nuclei with the specified arguments

## Custom Templates

### Using the Templates Directory

Nuclear Pond includes a script to download templates from a CSV file:

```bash
# Using yarn
yarn download-templates

# Using npm
npm run download-templates
```

This script:
1. Reads template URLs from `ice_templates.csv`
2. Downloads each template to the `./templates/` directory
3. Provides a summary of downloaded templates

### Specifying Custom Templates

When running Nuclei, you can specify custom templates:

1. Using the direct execution script:
   ```bash
   # Modify package.json to change the template path
   # "run:nuclei": "bin/nuclei -l targets.txt -t /path/to/custom/templates/"
   ```

2. Using Nuclear Pond:
   ```bash
   # Modify the command in package.json
   # "run:nuclear_pond_cli": "./nuclearpond local -l targets.txt -a LXQgLg== -c 4 -b 10 -p /path/to/custom/templates/"
   ```

## Troubleshooting

### Common Issues

1. **Nuclei not found**: Ensure the installation script completed successfully and the binary is in the `bin` directory.

2. **Permission denied**: On Unix-like systems, make sure the Nuclei binary is executable:
   ```bash
   chmod +x bin/nuclei
   ```

3. **Template errors**: If you encounter template errors, try validating the templates:
   ```bash
   bin/nuclei -validate -t ./templates/
   ```

4. **Base64 encoding issues**: When specifying Nuclei arguments with the `-a` flag, ensure they are properly base64 encoded:
   ```bash
   # Example: Encoding "-t dns"
   echo -ne "-t dns" | base64
   # Output: LXQgZG5z
   ```

## Advanced Usage

### Parallel Scanning

Nuclear Pond allows you to run Nuclei scans in parallel:

```bash
# Run with 8 threads and batch size of 10
./nuclearpond local -l targets.txt -a LXQgLg== -c 8 -b 10 -p ./templates/
```

### Combining with Other Tools

You can integrate Nuclear Pond's Nuclei scanning with other security tools:

```bash
# Example: Scan subdomains found by subfinder
subfinder -d example.com | tee subdomains.txt
./nuclearpond local -l subdomains.txt -a LXQgLg== -c 8 -b 10 -p ./templates/
```

