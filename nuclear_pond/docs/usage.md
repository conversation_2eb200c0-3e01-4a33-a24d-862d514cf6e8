# Usage Guide

This guide explains how to use Nuclear Pond for running nuclei scans in parallel, both in the cloud using AWS Lambda and locally on your machine.

## Command Overview

Nuclear Pond provides the following commands:

- `run`: Execute nuclei scans in the cloud using AWS Lambda
- `local`: Execute nuclei scans locally on your machine
- `service`: Start an API server for executing scans

To see the available options for each command, use the `--help` flag:

```bash
nuclearpond run --help
nuclearpond local --help
nuclearpond service --help
```

## Cloud Execution (AWS)

The `run` command executes nuclei scans in the cloud using AWS Lambda. This mode requires AWS credentials and a configured Lambda function.

### Basic Usage

```bash
nuclearpond run -t example.com -a $(echo -ne "-t dns" | base64) -o cmd -r eu-central-1 -f your-lambda-function -b 1 -c 1
```

### Options

- `-t, --target`: Specify a single target
- `-l, --targets`: Specify a file containing multiple targets
- `-a, --args`: Nuclei arguments as a base64-encoded string
- `-o, --output`: Output type (cmd, json, s3)
- `-r, --region`: AWS region
- `-f, --function-name`: AWS Lambda function name
- `-b, --batch-size`: Number of targets per batch
- `-c, --threads`: Number of parallel threads
- `-s, --silent`: Suppress output

### Environment Variables

Instead of specifying the AWS region and Lambda function name with flags, you can set them as environment variables:

```bash
export AWS_REGION=eu-central-1
export AWS_LAMBDA_FUNCTION_NAME=arn:aws:lambda:eu-central-1:067339277019:function:nuclear-pond-test-function

# for service (if local only)
export AWS_DYNAMODB_TABLE=arn:aws:dynamodb:eu-central-1:067339277019:table/nuclear-pond-test-server-state
```

## Local Execution

The `local` command executes nuclei scans locally on your machine, bypassing the need for AWS infrastructure.

### Basic Usage

```bash
nuclearpond local -t example.com -a $(echo -ne "-t dns" | base64) -c 4 -b 1
```

### Options

- `-t, --target`: Specify a single target
- `-l, --targets`: Specify a file containing multiple targets
- `-a, --args`: Nuclei arguments as a base64-encoded string
- `-b, --batch-size`: Number of targets per batch
- `-c, --threads`: Number of parallel threads
- `-p, --templates`: Path to nuclei templates (optional)
- `-s, --silent`: Suppress output

### Using Custom Templates

To use custom templates with the local execution mode, use the `-p` flag to specify the path to your templates directory:

```bash
nuclearpond local -l targets.txt -c 4 -b 10 -p ./templates
```

### Using npm/yarn Scripts

Nuclear Pond provides several npm/yarn scripts to simplify the execution of nuclei scans:

#### Installing Nuclei

```bash
# Install nuclei binary in the bin directory
yarn install:nuclei
# or
npm run install:nuclei
```

#### Running Nuclei Directly

```bash
# Run nuclei directly with the targets.txt file and templates directory
yarn run:nuclei
# or
npm run run:nuclei
```

#### Running Nuclear Pond with Local Nuclei

```bash
# Run nuclearpond local command with the installed nuclei binary
yarn run:nuclear_pond_cli
# or
npm run run:nuclear_pond_cli
```

These scripts automatically use the locally installed nuclei binary in the `bin` directory if available, or fall back to the system-installed nuclei if not found.

## API Server

The `service` command starts an API server that allows you to execute scans via HTTP requests.

### Basic Usage

```bash
nuclearpond service
```

The server will start on port 8080 by default.

### API Endpoints

- `GET /`: Index page
- `GET /health-check`: Health check endpoint
- `POST /scan`: Start a new scan
- `GET /scan/{id}`: Get the status of a scan

For more information on using the API, refer to the [API Guide](api.md).

## Deployment Options

Nuclear Pond can be deployed in multiple ways depending on your needs:

### 1. Local Development

Run Nuclear Pond locally for development and testing:

```bash
# Run locally with local nuclei execution
nuclearpond local -l targets.txt -c 4 -b 10

# Start local API server
nuclearpond service
```

### 2. AWS ECS Deployment

Deploy Nuclear Pond as a containerized service on AWS ECS Fargate:

```bash
# Deploy infrastructure using Terraform
cd terraform
terraform apply

# Deploy the application container
cd nuclear_pond_backend
./deploy-backend-dev.sh latest

# Access the service
curl http://$(terraform output -raw nuclear_pond_backend_alb_dns_name)/health-check
```

### 3. Serverless Lambda Only

Use only the Lambda function for scan execution without the API server:

```bash
# Deploy only Lambda infrastructure
terraform apply -target=aws_lambda_function.function

# Invoke directly via AWS CLI
aws lambda invoke --function-name nuclear-pond-function \
  --payload '{"Targets":["example.com"],"Args":["-t","dns"],"Output":"json"}' \
  response.json
```

### 4. Hybrid Deployment

Combine local development with cloud execution:

```bash
# Use local Nuclear Pond with cloud Lambda execution
nuclearpond run -t example.com -a $(echo -ne "-t dns" | base64) \
  -o json -r us-east-1 -f your-lambda-function
```

## Infrastructure Management

### Modular Deployment

The Terraform infrastructure is organized into independent modules:

```bash
# Deploy only networking
terraform apply -target=module.network

# Deploy only Nuclear Pond backend
terraform apply -target=module.nuclear_pond_backend

# Deploy only frontend
terraform apply -target=module.frontend

# Deploy everything
terraform apply
```

### Environment Management

Support for multiple environments:

```bash
# Development environment
terraform apply -var="nuclear_pond_environment=dev"

# Production environment
terraform apply -var="nuclear_pond_environment=prod" \
                -var="nuclear_pond_task_cpu=1024" \
                -var="nuclear_pond_task_memory=2048" \
                -var="nuclear_pond_desired_count=3"
```

### Automated Deployment

Use the generated deployment scripts for automated deployments:

```bash
# Development deployment
cd terraform/nuclear_pond_backend
./deploy-backend-dev.sh latest

# Production deployment with versioned image
./deploy-backend-prod.sh v1.2.3
```

## Monitoring and Operations

### Health Monitoring

```bash
# Check service health
curl http://your-alb-dns-name/health-check

# Monitor ECS service
aws ecs describe-services --cluster your-cluster --services your-service

# View logs
aws logs tail /ecs/fast-scan/dev/nuclearpond --follow
```

### Scaling Operations

```bash
# Manual scaling
aws ecs update-service --cluster your-cluster \
  --service your-service --desired-count 3

```

## Examples

For examples of common use cases, see the [Examples Guide](examples.md).

## Related Documentation

- **[Terraform Infrastructure](../terraform/README.md)** - Complete infrastructure setup
- **[Nuclear Pond Backend Module](../terraform/nuclear_pond_backend/README.md)** - ECS deployment details
- **[Deployment Guide](../terraform/nuclear_pond_backend/DEPLOYMENT.md)** - Detailed deployment instructions
- **[Migration Guide](../terraform/MIGRATION_GUIDE.md)** - Upgrading from older versions
