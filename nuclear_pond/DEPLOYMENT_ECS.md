# Nuclear Pond Service Deployment on ECS Fargate

This document outlines the infrastructure components and deployment process for hosting the Nuclear Pond service as a Docker container on AWS ECS Fargate with an Application Load Balancer (ALB).

## Overview

The Nuclear Pond application, which provides an API for initiating and monitoring Nuclei scans (executed by AWS Lambda), is containerized using its Dockerfile. This setup deploys the container as a scalable and resilient service using AWS ECS Fargate, fronted by an ALB for request distribution and exposure.

## Terraform Components Added

The following Terraform files were added to the `terraform` directory to define this infrastructure:

1.  **`ecs_network.tf`**: Defines the networking infrastructure for the ECS service.
    *   **VPC (`aws_vpc`):** A dedicated Virtual Private Cloud (CIDR: `10.0.0.0/16`) to isolate the ECS resources.
    *   **Subnets (`aws_subnet`):** 
        *   Two public subnets (e.g., `********/24`, `********/24`) across two Availability Zones (AZs) for the ALB.
        *   Two private subnets (e.g., `********/24`, `********/24`) across two AZs for the Fargate tasks, enhancing security.
    *   **Internet Gateway (`aws_internet_gateway`):** Attached to the VPC to allow internet connectivity for public subnets.
    *   **NAT Gateway (`aws_nat_gateway`):** Deployed in one public subnet, allowing Fargate tasks in private subnets to initiate outbound connections (e.g., to ECR, AWS services) without being directly exposed to the internet.
    *   **Route Tables (`aws_route_table`):** For managing traffic flow within the VPC (public routes to IGW, private routes to NAT GW).

2.  **`ecs_service.tf`**: Defines the ECS, ECR, ALB, and related IAM resources.
    *   **ECR Repository (`aws_ecr_repository`):** A private container registry (`${var.project_name}/nuclearpond`) to store the `nuclearpond` Docker image.
    *   **ECS Cluster (`aws_ecs_cluster`):** A logical grouping for the Nuclear Pond service.
    *   **IAM Roles & Policies:**
        *   `ecs_task_execution_role`: Allows ECS to pull images from ECR and send logs to CloudWatch.
        *   `nuclear_pond_task_role` & `nuclear_pond_task_policy`: Grants the Nuclear Pond application (running in Fargate) permissions to invoke the specified Lambda function and access the specified DynamoDB table.
    *   **CloudWatch Log Group (`aws_cloudwatch_log_group`):** For collecting logs from the Fargate tasks (e.g., `/ecs/${var.project_name}/nuclearpond`).
    *   **ECS Task Definition (`aws_ecs_task_definition`):** Describes the container to be run.
        *   CPU: `256` (`0.25 vCPU`)
        *   Memory: `512` MB (`0.5 GB`)
        *   Port Mapping: Container port `8080` exposed.
        *   Environment Variables:
            *   `NUCLEARPOND_API_KEY`: API key for the service (placeholder: `YOUR_API_KEY_HERE`).
            *   `AWS_REGION`: AWS region for the service to operate in.
            *   `AWS_LAMBDA_FUNCTION_NAME`: Name of the Lambda function that executes Nuclei scans (references `aws_lambda_function.function.function_name`).
            *   `AWS_DYNAMODB_TABLE`: Name of the DynamoDB table for scan states (references `aws_dynamodb_table.scan_state_table.name`).
        *   Image: Points to the image in the ECR repository.
    *   **Application Load Balancer (`aws_lb`):** Internet-facing ALB to distribute incoming HTTP requests.
    *   **ALB Target Group (`aws_lb_target_group`):** Routes requests to Fargate tasks on port `8080`, with health checks on `/health-check`.
    *   **ALB Listener (`aws_lb_listener`):** Listens for HTTP traffic on port `80` and forwards it to the target group.
    *   **ECS Service (`aws_ecs_service`):** Manages the running Fargate tasks, ensuring the desired number of tasks are running and registered with the ALB. Deploys tasks in the private subnets.
    *   **Security Groups (`aws_security_group`):
        *   ALB Security Group: Allows inbound HTTP (port 80) from the internet.
        *   Fargate Service Security Group: Allows inbound TCP (port 8080) only from the ALB.

## Key Configuration Points & Pre-deployment Setup

Before applying the Terraform configuration, ensure the following are addressed:

1.  **`NUCLEARPOND_API_KEY`**: 
    The `NUCLEARPOND_API_KEY` for the service is now managed via a Terraform variable named `nuclearpond_api_key` (defined in `variables.tf`).
    We must provide a value for this variable when running Terraform commands. There are several ways to do this:
    *   **Using a `terraform/terraform.tfvars` file:**
    *   **Using command-line arguments:** Pass the variable directly during `plan` or `apply`:
        ```bash
        terraform apply -var="nuclearpond_api_key=YOUR_CHOSEN_STRONG_API_KEY"
        ```
    *   **Using environment variables:** Prefix the variable name with `TF_VAR_`:
        ```bash
        export TF_VAR_nuclearpond_api_key="YOUR_CHOSEN_STRONG_API_KEY"
        terraform apply
        ```
    For enhanced security in later stages (beyond prototype), we gonna consider retrieving this key from AWS Secrets Manager and referencing it within the task definition instead.

## Deployment Steps

**Prerequisites:**
*   AWS CLI installed and configured with appropriate permissions.
*   Terraform CLI installed.
*   Docker installed.

**Deployment Process:**

1.  **Navigate to Terraform Directory:**
    ```bash
    cd terraform
    ```

2.  **Initialize Terraform:**
    ```bash
    terraform init
    ```

3.  **Review Terraform Plan:**
    ```bash
    terraform plan
    ```
    Carefully review the resources that will be created/modified.

4.  **Apply Terraform Configuration:**
    ```bash
    terraform apply
    ```
    Confirm the apply when prompted. This will create all the AWS resources, including the ECR repository.

5.  **Build and Push Docker Image:**
    *   After `terraform apply` completes, it will output `nuclear_pond_ecr_repository_url`.
    *   Use the provided script to build and push your Docker image:
        ```bash
        # Run the script (from terraform directory)
        ./scripts/build_and_push_ecs_image.sh $(terraform output -raw nuclear_pond_ecr_repository_url) YOUR_IMAGE_TAG
        # Example with 'latest' tag:
        # ./scripts/build_and_push_ecs_image.sh $(terraform output -raw nuclear_pond_ecr_repository_url) latest
        # Example with a version tag:
        # ./scripts/build_and_push_ecs_image.sh $(terraform output -raw nuclear_pond_ecr_repository_url) v1.0.1
        ```
    *   The script handles logging into ECR, building the image from `../../nuclear_pond/Dockerfile`, tagging it appropriately, and pushing it to the ECR repository.

6.  **Accessing the Service:**
    *   Once the ECS service is running with the pushed image, it can be accessed via the DNS name of the Application Load Balancer. This DNS name is outputted by Terraform as `nuclear_pond_alb_dns_name`:
        ```bash
        terraform output nuclear_pond_alb_dns_name
        ```
    *   The service will be available on HTTP at this DNS name (e.g., `http://<alb-dns-name>`).

## Updating the Service

*   **Application Code Changes:**
    1.  Make your code changes in the `nuclear_pond` directory.
    2.  Re-run the `scripts/build_and_push_ecs_image.sh` script with a new tag (or `latest`).
    3.  If you used a new tag, update the `image` field in the `aws_ecs_task_definition.nuclear_pond_task_def` resource within `ecs_service.tf` to point to the new image tag.
    4.  Run `terraform plan` and `terraform apply`.
    5.  Alternatively, if you pushed with the same tag (e.g., `latest`) that the task definition already uses, you might need to force a new deployment for the ECS service to pick up the updated image. This can be done via the AWS console or by slightly changing a parameter in the task definition (like an environment variable dummy value) and re-applying Terraform, or by using `aws ecs update-service --cluster <cluster-name> --service <service-name> --force-new-deployment`.

*   **Infrastructure Changes:**
    1.  Modify the Terraform (`.tf`) files as needed.
    2.  Run `terraform plan` and `terraform apply`. 