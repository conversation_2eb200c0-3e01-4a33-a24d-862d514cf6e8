package core

import (
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"sync"
	"time"
)

// ExecuteLocalScans runs nuclei scans locally in parallel
func ExecuteLocalScans(batches [][]string, templatesPath string, nucleiArgs []string, threads int, silent bool) {
	// Get start time
	start := time.Now()

	// Create a WaitGroup to wait for the goroutines to finish
	var wg sync.WaitGroup

	// Set the number of threads to use
	numThreads := threads

	// Create a channel to pass tasks to the goroutines
	tasks := make(chan func())

	// Start the goroutines
	for i := 0; i < numThreads; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for task := range tasks {
				task()
			}
		}()
	}

	// Add tasks to the channel
	for batchIndex, batch := range batches {
		// Create a temporary file for this batch of targets
		tempTargetFile := filepath.Join(os.TempDir(), fmt.Sprintf("nuclei_targets_%d.txt", batchIndex))

		// Write targets to the temporary file
		err := writeTargetsToFile(batch, tempTargetFile)
		if err != nil {
			log.Printf("Error writing targets to file: %v", err)
			continue
		}

		// Copy nucleiArgs to avoid modifying the original
		args := make([]string, len(nucleiArgs))
		copy(args, nucleiArgs)

		// Add template path if provided
		if templatesPath != "" {
			args = append(args, "-t", templatesPath)
		}

		// Add target file
		args = append(args, "-l", tempTargetFile)

		batchCopy := batch
		tempFileCopy := tempTargetFile
		taskArgs := args

		tasks <- func() {
			if !silent {
				log.Printf("Running nuclei against %d targets with args: %v", len(batchCopy), taskArgs)
			}

			// Run nuclei with the arguments
			output, err := runNucleiLocal(taskArgs)
			if err != nil {
				log.Printf("Error running nuclei: %v\nOutput: %s", err, output)
			} else if !silent {
				fmt.Println(output)
			}

			// Clean up the temporary file
			os.Remove(tempFileCopy)
		}
	}

	close(tasks)
	wg.Wait()

	// Print the results if not silent mode
	if !silent {
		log.Println("Completed all parallel operations in", time.Since(start))
	}
}

// runNucleiLocal runs the nuclei binary with the given arguments
func runNucleiLocal(args []string) (string, error) {
	// Get the current working directory
	cwd, err := os.Getwd()
	if err != nil {
		return "", fmt.Errorf("failed to get current working directory: %v", err)
	}

	// First check if nuclei is in the bin directory (relative to current directory)
	binPath := filepath.Join(cwd, "bin", "nuclei")
	if _, err := os.Stat(binPath); err == nil {
		// Run the nuclei binary from bin directory
		cmd := exec.Command(binPath, args...)
		output, err := cmd.CombinedOutput()
		return string(output), err
	}

	// If not in bin directory, check if nuclei is installed in PATH
	nucleiPath, err := exec.LookPath("nuclei")
	if err != nil {
		return "", fmt.Errorf("nuclei not found in PATH or bin directory: %v", err)
	}

	// Run the nuclei binary with the arguments
	cmd := exec.Command(nucleiPath, args...)
	output, err := cmd.CombinedOutput()
	return string(output), err
}

// writeTargetsToFile writes a list of targets to a file
func writeTargetsToFile(targets []string, filename string) error {
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	for _, target := range targets {
		_, err := file.WriteString(target + "\n")
		if err != nil {
			return err
		}
	}

	return nil
}
