package server

import (
	"log"
	"net/http"
	"os"
)

// checkAP<PERSON><PERSON><PERSON> verifies if the provided API key in the request header matches
// the NUCLEARPOND_API_KEY environment variable.
func checkAPI<PERSON>ey(r *http.Request) bool {
	expectedAPIKey := os.Getenv("NUCLEARPOND_API_KEY")

	if expectedAPIKey == "" {
		// This state should ideally be prevented by serverCheck at startup.
		// Logging here as an additional safeguard, though serverCheck will log.Fatal.
		log.Println("Warning: NUCLEARPOND_API_KEY is not set in the environment; API key check will consequently fail.")
		return false
	}

	requestAPIKey := r.Header.Get("X-NuclearPond-API-Key")
	if requestAPIKey != expectedAPIKey {
		// Avoid logging the actual key received from the client for security.
		// log.Println("Invalid API key provided by client.")
		return false
	}
	return true
}
