id: CVE-2021-22205

info:
  name: GitLab CE/EE - Remote Code Execution
  author: GitLab Red Team
  severity: critical
  description: GitLab CE/EE starting from 11.9 does not properly validate image files that were passed to a file parser, resulting in a remote command execution vulnerability. This template attempts to passively identify vulnerable versions of GitLab without the need for an exploit by matching unique hashes for the application-<hash>.css file in the header for unauthenticated requests. Positive matches do not guarantee exploitability. Tooling to find relevant hashes based on the semantic version ranges specified in the CVE is linked in the references section below.
  impact: |
    Successful exploitation of this vulnerability could allow an attacker to execute arbitrary code on the affected GitLab instance.
  remediation: |
    Upgrade to GitLab CE/EE version 13.10.3 or 13.11.1 to mitigate this vulnerability.
  reference:
    - https://gitlab.com/gitlab-com/gl-security/security-operations/gl-redteam/red-team-research/cve-2021-22205-hash-generator
    - https://gitlab.com/gitlab-com/gl-security/security-operations/gl-redteam/red-team-operations/-/issues/196
    - https://gitlab.com/gitlab-org/cves/-/blob/master/2021/CVE-2021-22205.json
    - https://censys.io/blog/cve-2021-22205-it-was-a-gitlab-smash/
    - https://security.humanativaspa.it/gitlab-ce-cve-2021-22205-in-the-wild/
    - https://hackerone.com/reports/1154542
    - https://nvd.nist.gov/vuln/detail/CVE-2021-22205
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H
    cvss-score: 10
    cve-id: CVE-2021-22205
    cwe-id: CWE-94
    epss-score: 0.97463
    epss-percentile: 0.99959
    cpe: cpe:2.3:a:gitlab:gitlab:*:*:*:*:community:*:*:*
  metadata:
    max-request: 1
    vendor: gitlab
    product: gitlab
    shodan-query:
      - http.title:"GitLab"
      - cpe:"cpe:2.3:a:gitlab:gitlab"
      - http.title:"gitlab"
    fofa-query: title="gitlab"
    google-query: intitle:"gitlab"
  tags: cve2021,cve,kev,hackerone,gitlab,rce

http:
  - method: GET
    path:
      - "{{BaseURL}}/users/sign_in"

    host-redirects: true
    max-redirects: 3
    matchers:
      - type: word
        words:
          - "015d088713b23c749d8be0118caeb21039491d9812c75c913f48d53559ab09df"
          - "02aa9533ec4957bb01d206d6eaa51d762c7b7396362f0f7a3b5fb4dd6088745b"
          - "051048a171ccf14f73419f46d3bd8204aa3ed585a72924faea0192f53d42cfce"
          - "08858ced0ff83694fb12cf155f6d6bf450dcaae7192ea3de8383966993724290"
          - "0993beabc8d2bb9e3b8d12d24989426b909921e20e9c6a704de7a5f1dfa93c59"
          - "0a5b4edebfcb0a7be64edc06af410a6fbc6e3a65b76592a9f2bcc9afea7eb753"
          - "1084266bd81c697b5268b47c76565aa86b821126a6b9fe6ea7b50f64971fc96f"
          - "14c313ae08665f7ac748daef8a70010d2ea9b52fd0cae594ffa1ffa5d19c43f4"
          - "1626b2999241b5a658bddd1446648ed0b9cc289de4cc6e10f60b39681a0683c4"
          - "20f01320ba570c73e01af1a2ceb42987bcb7ac213cc585c187bec2370cf72eb6"
          - "27d2c4c4e2fcf6e589e3e1fe85723537333b087003aa4c1d2abcf74d5c899959"
          - "292ca64c0c109481b0855aea6b883a588bd293c6807e9493fc3af5a16f37f369"
          - "2eaf7e76aa55726cc0419f604e58ee73c5578c02c9e21fdbe7ae887925ea92ae"
          - "30a9dffe86b597151eff49443097496f0d1014bb6695a2f69a7c97dc1c27828f"
          - "318ee33e5d14035b04832fa07c492cdf57788adda50bb5219ef75b735cbf00e2"
          - "33313f1ff2602ef43d945e57e694e747eb00344455ddb9b2544491a3af2696a1"
          - "335f8ed58266e502d415f231f6675a32bb35cafcbaa279baa2c0400d4a9872ac"
          - "34031b465d912c7d03e815c7cfaff77a3fa7a9c84671bb663026d36b1acd3f86"
          - "3407a4fd892e9d5024f3096605eb1e25cad75a8bf847d26740a1e6a77e45b087"
          - "340c31a75c5150c5e501ec143849adbed26fed0da5a5ee8c60fb928009ea3b86"
          - "38981e26a24308976f3a29d6e5e2beef57c7acda3ad0d5e7f6f149d58fd09d3d"
          - "3963d28a20085f0725884e2dbf9b5c62300718aa9c6b4b696c842a3f4cf75fcd"
          - "39b154eeefef684cb6d56db45d315f8e9bf1b2cc86cf24d8131c674521f5b514"
          - "39fdbd63424a09b5b065a6cc60c9267d3f49950bf1f1a7fd276fe1ece4a35c09"
          - "3b51a43178df8b4db108a20e93a428a889c20a9ed5f41067d1a2e8224740838e"
          - "3cbf1ae156fa85f16d4ca01321e0965db8cfb9239404aaf52c3cebfc5b4493fb"
          - "40d8ac21e0e120f517fbc9a798ecb5caeef5182e01b7e7997aac30213ef367b3"
          - "4448d19024d3be03b5ba550b5b02d27f41c4bdba4db950f6f0e7136d820cd9e1"
          - "450cbe5102fb0f634c533051d2631578c8a6bae2c4ef1c2e50d4bfd090ce3b54"
          - "455d114267e5992b858fb725de1c1ddb83862890fe54436ffea5ff2d2f72edc8"
          - "4568941e60dbfda3472e3f745cd4287172d4e6cce44bed85390af9e4e2112d0b"
          - "45b2cf643afd34888294a073bf55717ea00860d6a1dca3d301ded1d0040cac44"
          - "473ef436c59830298a2424616d002865f17bb5a6e0334d3627affa352a4fc117"
          - "4990bb27037f3d5f1bffc0625162173ad8043166a1ae5c8505aabe6384935ce2"
          - "4a081f9e3a60a0e580cad484d66fbf5a1505ad313280e96728729069f87f856e"
          - "4abc4e078df94075056919bd59aed6e7a0f95067039a8339b8f614924d8cb160"
          - "504940239aafa3b3a7b49e592e06a0956ecaab8dbd4a5ea3a8ffd920b85d42eb"
          - "52560ba2603619d2ff1447002a60dcb62c7c957451fb820f1894e1ce7c23821c"
          - "530a8dd34c18ca91a31fbae2f41d4e66e253db0343681b3c9640766bf70d8edf"
          - "5440e2dd89d3c803295cc924699c93eb762e75d42178eb3fe8b42a5093075c71"
          - "62e4cc014d9d96f9cbf443186289ffd9c41bdfe951565324891dcf38bcca5a51"
          - "64e10bc92a379103a268a90a7863903eacb56843d8990fff8410f9f109c3b87a"
          - "655ad8aea57bdaaad10ff208c7f7aa88c9af89a834c0041ffc18c928cc3eab1f"
          - "67ac5da9c95d82e894c9efe975335f9e8bdae64967f33652cd9a97b5449216d2"
          - "69a1b8e44ba8b277e3c93911be41b0f588ac7275b91a184c6a3f448550ca28ca"
          - "6ae610d783ba9a520b82263f49d2907a52090fecb3ac37819cea12b67e6d94fb"
          - "70ce56efa7e602d4b127087b0eca064681ecdd49b57d86665da8b081da39408b"
          - "7310c45f08c5414036292b0c4026f281a73cf8a01af82a81257dd343f378bbb5"
          - "73a21594461cbc9a2fb00fc6f94aec1a33ccf435a7d008d764ddd0482e08fc8d"
          - "77566acc818458515231d0a82c131a42890d771ea998b9f578dc38e0eb7e517f"
          - "78812856e55613c6803ecb31cc1864b7555bf7f0126d1dfa6f37376d37d3aeab"
          - "79837fd1939f90d58cc5a842a81120e8cecbc03484362e88081ebf3b7e3830e9"
          - "7b1dcbacca4f585e2cb98f0d48f008acfec617e473ba4fd88de36b946570b8b9"
          - "7f1c7b2bfaa6152740d453804e7aa380077636cad101005ed85e70990ec20ec5"
          - "81c5f2c7b2c0b0abaeb59585f36904031c21b1702c24349404df52834fbd7ad3"
          - "83dc10f687305b22e602ba806619628a90bd4d89be7c626176a0efec173ecff1"
          - "93ebf32a4bd988b808c2329308847edd77e752b38becc995970079a6d586c39b"
          - "969119f639d0837f445a10ced20d3a82d2ea69d682a4e74f39a48a4e7b443d5e"
          - "9b4e140fad97320405244676f1a329679808e02c854077f73422bd8b7797476b"
          - "9c095c833db4364caae1659f4e4dcb78da3b5ec5e9a507154832126b0fe0f08e"
          - "a0c92bafde7d93e87af3bc2797125cba613018240a9f5305ff949be8a1b16528"
          - "a9308f85e95b00007892d451fd9f6beabcd8792b4c5f8cd7524ba7e941d479c9"
          - "ac9b38e86b6c87bf8db038ae23da3a5f17a6c391b3a54ad1e727136141a7d4f5"
          - "ae0edd232df6f579e19ea52115d35977f8bdbfa9958e0aef2221d62f3a39e7d8"
          - "aeddf31361633b3d1196c6483f25c484855e0f243e7f7e62686a4de9e10ec03b"
          - "b50bfeb87fe7bb245b31a0423ccfd866ca974bc5943e568ce47efb4cd221d711"
          - "b64a1277a08c2901915525143cd0b62d81a37de0a64ec135800f519cb0836445"
          - "bb1565ffd7c937bea412482ed9136c6057be50356f1f901379586989b4dfe2ca"
          - "be9a23d3021354ec649bc823b23eab01ed235a4eb730fd2f4f7cdb2a6dee453a"
          - "bec9544b57b8b2b515e855779735ad31c3eacf65d615b4bfbd574549735111e7"
          - "bf1ba5d5d3395adc5bad6f17cc3cb21b3fb29d3e3471a5b260e0bc5ec7a57bc4"
          - "bf1c397958ee5114e8f1dadc98fa9c9d7ddb031a4c3c030fa00c315384456218"
          - "c8d8d30d89b00098edab024579a3f3c0df2613a29ebcd57cdb9a9062675558e4"
          - "c923fa3e71e104d50615978c1ab9fcfccfcbada9e8df638fc27bf4d4eb72d78c"
          - "d0850f616c5b4f09a7ff319701bce0460ffc17ca0349ad2cf7808b868688cf71"
          - "d161b6e25db66456f8e0603de5132d1ff90f9388d0a0305d2d073a67fd229ddb"
          - "d56f0577fbbbd6f159e9be00b274270cb25b60a7809871a6a572783b533f5a3c"
          - "d812b9bf6957fafe35951054b9efc5be6b10c204c127aa5a048506218c34e40f"
          - "dc6b3e9c0fad345e7c45a569f4c34c3e94730c33743ae8ca055aa6669ad6ac56"
          - "def1880ada798c68ee010ba2193f53a2c65a8981871a634ae7e18ccdcd503fa3"
          - "e2578590390a9eb10cd65d130e36503fccb40b3921c65c160bb06943b2e3751a"
          - "e4b6f040fe2e04c86ed1f969fc72710a844fe30c3501b868cb519d98d1fe3fd0"
          - "eb078ffe61726e3898dc9d01ea7955809778bde5be3677d907cbd3b48854e687"
          - "ec9dfedd7bd44754668b208858a31b83489d5474f7606294f6cc0128bb218c6d"
          - "ed4780bb05c30e3c145419d06ad0ab3f48bd3004a90fb99601f40c5b6e1d90fd"
          - "ef53a4f4523a4a0499fb892d9fb5ddb89318538fef33a74ce0bf54d25777ea83"
          - "f154ef27cf0f1383ba4ca59531058312b44c84d40938bc8758827023db472812"
          - "f7d1309f3caef67cb63bd114c85e73b323a97d145ceca7d6ef3c1c010078c649"
          - "f9ab217549b223c55fa310f2007a8f5685f9596c579f5c5526e7dcb204ba0e11"
        condition: or

    extractors:
      - type: regex
        group: 1
        regex:
          - '(?:application-)(\S{64})(?:\.css)'
# digest: 4a0a00473045022100ce6c0443de847c08cddaa5407f061eeb0ceb63491ac9ec369de50e9f6e011c1f02207adbfef79c2a2a9b13d7b1f6c5ce30567a2cdc639c9c9d7449027bfe69440b3d:922c64590222798bb761d5b6d8e72950