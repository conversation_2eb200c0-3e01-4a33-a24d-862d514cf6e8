id: CVE-2021-21985

info:
  name: VMware vSphere Client (HTML5) - Remote Code Execution
  author: D0rkerDevil
  severity: critical
  description: |
    The vSphere Client (HTML5) contains a remote code execution vulnerability due to lack of input validation in the Virtual SAN Health Check plug-in which is enabled by default in vCenter Server. A malicious actor with network access to port 443 may exploit this issue to execute commands with unrestricted privileges on the underlying operating system that hosts vCenter Server.
  impact: |
    Successful exploitation of this vulnerability could allow an attacker to execute arbitrary code on the affected system.
  remediation: |
    Apply the necessary security patches or updates provided by VMware to mitigate this vulnerability.
  reference:
    - https://www.vmware.com/security/advisories/VMSA-2021-0010.html
    - https://github.com/alt3kx/CVE-2021-21985_PoC
    - https://nvd.nist.gov/vuln/detail/CVE-2021-21985
    - http://packetstormsecurity.com/files/162812/VMware-Security-Advisory-2021-0010.html
    - https://github.com/onSec-fr/CVE-2021-21985-Checker
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2021-21985
    cwe-id: CWE-20
    epss-score: 0.97371
    epss-percentile: 0.99903
    cpe: cpe:2.3:a:vmware:vcenter_server:6.5:-:*:*:*:*:*:*
  metadata:
    max-request: 1
    vendor: vmware
    product: vcenter_server
  tags: cve2021,cve,packetstorm,rce,vsphere,vmware,kev

http:
  - raw:
      - |
        POST /ui/h5-vsan/rest/proxy/service/com.vmware.vsan.client.services.capability.VsanCapabilityProvider/getClusterCapabilityData HTTP/1.1
        Host: {{Hostname}}
        Accept: */*
        Content-Type: application/json

        {"methodInput":[{"type":"ClusterComputeResource","value": null,"serverGuid": null}]}

    matchers:
      - type: word
        part: body
        words:
          - '{"result":{"isDisconnected":'
# digest: 4a0a00473045022100c571a92acad5d65c4d86227759f966a8775927b9ab893a43d216ff841e3a515d02200445ab899b09303ec7fb4b19371df6200789038c37cc2e807341abdca54c8f04:922c64590222798bb761d5b6d8e72950