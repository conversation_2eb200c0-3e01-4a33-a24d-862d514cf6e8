id: CVE-2021-21972

info:
  name: VMware vSphere Client (HTML5) - Remote Code Execution
  author: dwisiswant0
  severity: critical
  description: VMware vCenter vSphere Client (HTML5) contains a remote code execution vulnerability in a vCenter Server plugin. A malicious actor with network access to port 443 may exploit this issue to execute commands with unrestricted privileges on the underlying operating system that hosts vCenter Server. This affects VMware vCenter Server (7.x before 7.0 U1c, 6.7 before 6.7 U3l and 6.5 before 6.5 U3n) and VMware Cloud Foundation (4.x before 4.2 and 3.x before ********).
  impact: |
    Successful exploitation of this vulnerability could allow an attacker to execute arbitrary code on the affected system.
  remediation: |
    Apply the necessary security patches or updates provided by VMware to mitigate this vulnerability.
  reference:
    - https://swarm.ptsecurity.com/unauth-rce-vmware/
    - https://nvd.nist.gov/vuln/detail/CVE-2021-21972
    - https://www.vmware.com/security/advisories/VMSA-2021-0002.html
    - http://packetstormsecurity.com/files/161590/VMware-vCenter-Server-7.0-Arbitrary-File-Upload.html
    - https://github.com/NS-Sp4ce/CVE-2021-21972
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2021-21972
    cwe-id: CWE-22
    epss-score: 0.97299
    epss-percentile: 0.99858
    cpe: cpe:2.3:a:vmware:cloud_foundation:*:*:*:*:*:*:*:*
  metadata:
    max-request: 1
    vendor: vmware
    product: cloud_foundation
  tags: cve2021,cve,vmware,rce,vcenter,kev,packetstorm

http:
  - method: GET
    path:
      - "{{BaseURL}}/ui/vropspluginui/rest/services/getstatus"

    matchers-condition: and
    matchers:
      - type: word
        part: header
        words:
          - "VSPHERE-UI-JSESSIONID"
        condition: and

      - type: regex
        part: body
        regex:
          - "(Install|Config) Final Progress"

      - type: status
        status:
          - 200
# digest: 4a0a0047304502205f2f5ee6591528fe56ef45017123e87c12399b038a1ae0b8e94e014458edd3d5022100e326776cb6fbdc04200144e777b063d60f19891de3c1d89b64b952dde190d9ab:922c64590222798bb761d5b6d8e72950