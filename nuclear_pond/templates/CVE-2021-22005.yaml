id: CVE-2021-22005

info:
  name: VMware vCenter Server - Arbitrary File Upload
  author: PR3R00T
  severity: critical
  description: VMware vCenter Server contains an arbitrary file upload vulnerability in the Analytics service. A malicious actor with network access to port 443 on vCenter Server may exploit this issue to execute code on vCenter Server by uploading a specially crafted file.
  impact: |
    Allows an attacker to upload and execute arbitrary files on the target system
  remediation: |
    Apply the necessary security patches or updates provided by VMware
  reference:
    - https://kb.vmware.com/s/article/85717
    - https://www.vmware.com/security/advisories/VMSA-2021-0020.html
    - https://core.vmware.com/vmsa-2021-0020-questions-answers-faq
    - https://nvd.nist.gov/vuln/detail/CVE-2021-22005
    - https://github.com/ARPSyndicate/kenzer-templates
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2021-22005
    cwe-id: CWE-22
    epss-score: 0.97396
    epss-percentile: 0.99916
    cpe: cpe:2.3:a:vmware:cloud_foundation:*:*:*:*:*:*:*:*
  metadata:
    max-request: 2
    vendor: vmware
    product: cloud_foundation
  tags: cve2021,cve,vmware,vcenter,fileupload,kev,intrusive

http:
  - raw:
      - |
        GET / HTTP/1.1
        Host: {{Hostname}}
      - |
        POST /analytics/telemetry/ph/api/hyper/send?_c&_i=test HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/json

        test_data

    matchers:
      - type: dsl
        dsl:
          - "status_code_1 == 200"
          - "status_code_2 == 201"
          - "contains(body_1, 'VMware vSphere')"
          - "content_length_2 == 0"
        condition: and
# digest: 490a004630440220799b75d3b5638298f2be1b8780c7ee9d3214559ff22ff188e6978bca6e89e58e0220201b61f8331c48e256187039a9e59c27f9b626e234fb68bd1ab4feedc34d7603:922c64590222798bb761d5b6d8e72950