id: CVE-2024-1709

info:
  name: ConnectWise ScreenConnect 23.9.7 - Authentication Bypass
  author: johnk3r
  severity: critical
  description: |
    ConnectWise ScreenConnect 23.9.7 and prior are affected by an Authentication Bypass Using an Alternate Path or Channel vulnerability, which may allow an attacker direct access to confidential information or critical systems.
  reference:
    - https://www.huntress.com/blog/a-catastrophe-for-control-understanding-the-screenconnect-authentication-bypass
    - https://github.com/watchtowrlabs/connectwise-screenconnect_auth-bypass-add-user-poc
    - https://www.connectwise.com/company/trust/security-bulletins/connectwise-screenconnect-23.9.8
    - https://nvd.nist.gov/vuln/detail/CVE-2024-1709
    - https://github.com/rapid7/metasploit-framework/pull/18870
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H
    cvss-score: 10
    cve-id: CVE-2024-1709
    cwe-id: CWE-288,NVD-CWE-Other
    epss-score: 0.94464
    epss-percentile: 0.99213
    cpe: cpe:2.3:a:connectwise:screenconnect:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 1
    vendor: connectwise
    product: screenconnect
    shodan-query: http.favicon.hash:-82958153
    fofa-query:
      - app="ScreenConnect-Remote-Support-Software"
      - app="screenconnect-remote-support-software"
      - icon_hash=-82958153
    zoomeye-query: app="ScreenConnect Remote Management Software"
    hunter-query:
      - app.name="ConnectWise ScreenConnect software"
      - app.name="connectwise screenconnect software"
  tags: cve,cve2024,screenconnect,connectwise,auth-bypass,kev
variables:
  string: "{{rand_text_alpha(10)}}"

http:
  - method: GET
    path:
      - "{{BaseURL}}/SetupWizard.aspx/{{string}}"

    matchers-condition: and
    matchers:
      - type: word
        part: body
        words:
          - "SetupWizardPage"
          - "ContentPanel SetupWizard"
        condition: and

      - type: status
        status:
          - 200

    extractors:
      - type: kval
        part: header
        kval:
          - Server
# digest: 490a0046304402205a1b1b6d7aece0da929573841c73b56bb9cfd7bdcf2e94e24dba5cd4b7094bf7022030060662a89c3eda4a04a85ae7fc6c336d9bf514fe2104fca60465f180bbddb9:922c64590222798bb761d5b6d8e72950